/* Main App Container */
.superdesign-app {
    width: 100%;
    height: 100vh;
    display: flex;
    flex-direction: column;
    background: var(--vscode-sideBar-background);
    color: var(--vscode-sideBar-foreground);
    border-right: 1px solid var(--vscode-sideBar-border);
    font-family: var(--vscode-font-family);
    overflow: hidden;
    padding: 0;
    margin: 0;
}

/* App container variants for different contexts */
.superdesign-app.chat-view {
    background: var(--vscode-sideBar-background);
    color: var(--vscode-sideBar-foreground);
    border-right: 1px solid var(--vscode-sideBar-border);
}

.superdesign-app.chat-view.panel-layout {
    background: var(--vscode-panel-background);
    color: var(--vscode-panel-foreground);
    border-right: 1px solid var(--vscode-panel-border);
}

.superdesign-app.canvas-view {
    background: var(--vscode-panel-background);
    color: var(--vscode-panel-foreground);
    border-right: 1px solid var(--vscode-panel-border);
}

/* App Header */
.app-header {
    padding: 20px 24px 16px 24px;
    border-bottom: 1px solid var(--vscode-panel-border);
    background: var(--vscode-sideBar-background);
}

.app-header h1 {
    margin: 0 0 8px 0;
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--vscode-foreground);
}

.app-header p {
    margin: 0;
    color: var(--vscode-descriptionForeground);
    font-size: 0.9rem;
}

/* Chat Panel */
.chat-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
}

.chat-header {
    padding: 24px 24px 16px 24px;
    border-bottom: 1px solid var(--vscode-panel-border);
}

.chat-header h2 {
    margin: 0 0 8px 0;
    font-size: 1.2rem;
    font-weight: 500;
    color: var(--vscode-foreground);
}

.chat-header p {
    margin: 0;
    color: var(--vscode-descriptionForeground);
    font-size: 0.9rem;
}

/* Chat Container */
.chat-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
}

/* Chat History */
.chat-history {
    flex: 1;
    overflow-y: auto;
    padding: 20px 24px;
    display: flex;
    flex-direction: column;
    gap: 16px;
    scroll-behavior: smooth;
}

.chat-placeholder {
    text-align: center;
    padding: 40px 20px;
    color: var(--vscode-descriptionForeground);
}

.chat-placeholder p {
    margin: 0 0 16px 0;
    font-size: 1rem;
}

.chat-placeholder p:first-child {
    font-size: 1.1rem;
    font-weight: 500;
    color: var(--vscode-foreground);
}

.chat-placeholder ul {
    text-align: left;
    max-width: 300px;
    margin: 0 auto;
    padding-left: 20px;
}

.chat-placeholder li {
    margin: 8px 0;
    line-height: 1.4;
}

/* Chat Messages */
.chat-message {
    max-width: 85%;
    margin-bottom: 4px;
}

.chat-message.user {
    align-self: flex-end;
}

.chat-message.assistant {
    align-self: flex-start;
}

.message-header {
    margin-bottom: 6px;
}

.message-header strong {
    font-size: 0.85rem;
    color: var(--vscode-descriptionForeground);
    font-weight: 500;
}

.message-content {
    padding: 12px 16px;
    border-radius: 12px;
    line-height: 1.5;
    font-size: 0.95rem;
    word-wrap: break-word;
    white-space: pre-wrap;
}

.chat-message.user .message-content {
    background: var(--vscode-button-background);
    color: var(--vscode-button-foreground);
    border-bottom-right-radius: 4px;
}

.chat-message.assistant .message-content {
    background: var(--vscode-input-background);
    border: 1px solid var(--vscode-input-border);
    color: var(--vscode-foreground);
    border-bottom-left-radius: 4px;
}

.message-content.typing {
    font-style: italic;
    opacity: 0.8;
}

.typing-indicator {
    animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 0.4; }
    50% { opacity: 1; }
}

/* Chat Input Container */
.chat-input-container {
    border-top: 1px solid var(--vscode-panel-border);
    background: var(--vscode-sideBar-background);
    padding: 16px 24px 24px 24px;
}

/* Quick Suggestions */
.quick-suggestions {
    display: flex;
    gap: 8px;
    margin-bottom: 16px;
    flex-wrap: wrap;
}

.suggestion-btn {
    background: var(--vscode-button-secondaryBackground);
    color: var(--vscode-button-secondaryForeground);
    border: 1px solid var(--vscode-input-border);
    padding: 8px 12px;
    border-radius: 20px;
    cursor: pointer;
    font-size: 0.8rem;
    font-weight: 500;
    transition: all 0.2s ease;
    white-space: nowrap;
}

.suggestion-btn:hover:not(:disabled) {
    background: var(--vscode-button-secondaryHoverBackground);
    border-color: var(--vscode-focusBorder);
    transform: translateY(-1px);
}

.suggestion-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

/* Chat Input */
.chat-input {
    display: flex;
    gap: 12px;
    align-items: flex-end;
}

.message-input {
    flex: 1;
    padding: 12px 16px;
    background: var(--vscode-input-background);
    border: 2px solid var(--vscode-input-border);
    border-radius: 24px;
    color: var(--vscode-input-foreground);
    font-family: var(--vscode-font-family);
    font-size: 0.95rem;
    resize: none;
    outline: none;
    transition: border-color 0.2s ease;
}

.message-input:focus {
    border-color: var(--vscode-focusBorder);
}

.message-input::placeholder {
    color: var(--vscode-input-placeholderForeground);
}

.send-btn {
    background: var(--vscode-button-background);
    color: var(--vscode-button-foreground);
    border: none;
    padding: 12px 16px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 1rem;
    transition: all 0.2s ease;
    min-width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.send-btn:hover:not(:disabled) {
    background: var(--vscode-button-hoverBackground);
    transform: scale(1.05);
}

.send-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

/* Scrollbar Styling */
.chat-history::-webkit-scrollbar {
    width: 8px;
}

.chat-history::-webkit-scrollbar-track {
    background: var(--vscode-scrollbarSlider-background);
    border-radius: 4px;
}

.chat-history::-webkit-scrollbar-thumb {
    background: var(--vscode-scrollbarSlider-hoverBackground);
    border-radius: 4px;
}

.chat-history::-webkit-scrollbar-thumb:hover {
    background: var(--vscode-scrollbarSlider-activeBackground);
}

/* Responsive Design */
@media (max-width: 768px) {
    .chat-history {
        padding: 16px;
    }
    
    .chat-input-container {
        padding: 12px 16px;
    }
    
    .chat-message {
        max-width: 95%;
    }
}

/* Colors Panel */
.colors-panel h3 {
    margin: 0 0 20px 0;
    color: var(--vscode-foreground);
}

.chat-header p {
    margin: 0;
    color: var(--vscode-descriptionForeground);
    font-size: 0.9rem;
}

.control-btn, .viewport-btn {
    background-color: transparent;
    border: none;
    color: #ffffff;
    padding: 0;
    border-radius: 5px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    transition: background-color 0.15s ease;
}

.control-btn:hover, .viewport-btn:hover {
    background-color: #3f3f3f;
}

.control-btn:disabled, .viewport-btn:disabled {
    opacity: 0.4;
    cursor: not-allowed;
    background-color: transparent;
}

.control-btn.active,
.viewport-btn.active,
.viewport-toggle.active {
    background-color: #4f4f4f;
}

.viewport-divider {
    width: 1px;
    height: 24px;
    background-color: #4a4a4a;
    margin: 0 4px;
}

.zoom-indicator {
    padding: 0 10px;
    background-color: transparent;
    border-radius: 4px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 45px;
    font-weight: 500;
}

.viewport-controls {
    display: flex;
    align-items: center;
    background-color: #1e1e1e;
    border-radius: 5px;
    padding: 2px;
}

.viewport-controls .control-btn {
    width: 30px;
    height: 30px;
}

.canvas-info {
    position: absolute;
    top: 12px;
    right: 12px;
    background-color: #2c2c2c;
    color: #ffffff;
    padding: 8px 14px;
    border-radius: 8px;
    font-size: 12px;
    z-index: 100;
    white-space: nowrap;
    box-shadow: 0 4px 12px rgba(0,0,0,0.25);
}

/* Transform Wrapper */
.canvas-transform-wrapper {
    flex: 1;
    overflow: hidden;
    cursor: grab;
}

.canvas-transform-wrapper:active {
    cursor: grabbing;
}

.canvas-transform-content {
    position: relative;
    /* Grid background is handled by canvas-grid */
}

/* Canvas Grid */
.canvas-grid {
    position: relative;
    min-width: 10000px;
    min-height: 10000px;
    user-select: none;
    /* Always show subtle grid background */
    background-image: 
        linear-gradient(90deg, rgba(var(--vscode-panel-border-rgb, 128, 128, 128), 0.3) 1px, transparent 1px),
        linear-gradient(rgba(var(--vscode-panel-border-rgb, 128, 128, 128), 0.3) 1px, transparent 1px);
    background-size: 50px 50px;
    background-position: 0 0, 0 0;
    /* Fallback for browsers that don't support CSS custom property RGB values */
    background: 
        linear-gradient(90deg, var(--vscode-panel-border) 1px, transparent 1px),
        linear-gradient(var(--vscode-panel-border) 1px, transparent 1px);
    background-size: 50px 50px;
}

.canvas-grid.dragging {
    /* Enhance grid visibility during dragging with smaller, more visible grid */
    background-image: 
        linear-gradient(90deg, var(--vscode-focusBorder) 1px, transparent 1px),
        linear-gradient(var(--vscode-focusBorder) 1px, transparent 1px);
    background-size: 25px 25px;
    background-position: 0 0, 0 0;
}

/* Design Frames */
.design-frame {
    background: var(--vscode-editor-background);
    border: 2px solid var(--vscode-input-border);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.design-frame:hover {
    border-color: var(--vscode-focusBorder);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.design-frame.selected {
    border-color: var(--vscode-button-background);
    box-shadow: 0 0 0 2px var(--vscode-button-background);
}

.design-frame.dragging {
    border-color: var(--vscode-focusBorder);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    transform: scale(1.02);
    transition: none;
}

.frame-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    background: var(--vscode-sideBar-background);
    border-bottom: 1px solid var(--vscode-panel-border);
    border-radius: 6px 6px 0 0;
    height: 50px;
    box-sizing: border-box;
    gap: 8px;
    user-select: none;
}

.frame-title {
    font-size: 12px;
    font-weight: 500;
    color: var(--vscode-foreground);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex: 1;
}

.frame-meta {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-shrink: 0;
}

.frame-size {
    font-size: 10px;
    color: var(--vscode-descriptionForeground);
    background: var(--vscode-badge-background);
    color: var(--vscode-badge-foreground);
    padding: 2px 6px;
    border-radius: 10px;
    font-weight: 500;
}

.frame-dimensions {
    font-size: 9px;
    color: var(--vscode-descriptionForeground);
    background: var(--vscode-input-background);
    border: 1px solid var(--vscode-input-border);
    padding: 1px 4px;
    border-radius: 3px;
    font-family: var(--vscode-editor-font-family);
}

/* Frame Viewport Controls */
.frame-viewport-controls {
    display: flex;
    align-items: center;
    gap: 2px;
    background: var(--vscode-input-background);
    border: 1px solid var(--vscode-input-border);
    border-radius: 4px;
    padding: 1px;
}

.frame-viewport-btn {
    background: transparent;
    border: none;
    padding: 2px 4px;
    border-radius: 3px;
    cursor: pointer;
    font-size: 10px;
    transition: background-color 0.2s ease;
    color: var(--vscode-foreground);
    line-height: 1;
}

.frame-viewport-btn:hover {
    background: var(--vscode-list-hoverBackground);
}

.frame-viewport-btn.active {
    background: var(--vscode-button-background);
    color: var(--vscode-button-foreground);
}

/* Frame Viewport Indicator */
.frame-viewport-indicator {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 10px;
    color: var(--vscode-descriptionForeground);
}

.global-indicator {
    font-size: 8px;
    opacity: 0.7;
}

.viewport-icon {
    font-size: 10px;
}

.frame-status {
    font-size: 8px;
    line-height: 1;
}

.frame-status.loading {
    color: var(--vscode-progressBar-background);
    animation: pulse 1.5s ease-in-out infinite;
}

.frame-status.error {
    color: var(--vscode-errorForeground);
}

.frame-status.loaded {
    color: var(--vscode-charts-green);
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Viewport Controls */
.viewport-toggle.active {
    background: var(--vscode-button-background);
    color: var(--vscode-button-foreground);
}

.frame-content {
    position: relative;
    height: calc(100% - 50px);
    padding: 0;
    overflow: hidden;
    background: white;
}

.frame-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: #ffffff;
    color: #333;
    padding: 20px;
    text-align: center;
    border-radius: 0 0 6px 6px;
}

.placeholder-icon {
    font-size: 32px;
    margin-bottom: 12px;
    opacity: 0.6;
}

.placeholder-name {
    margin: 0 0 8px 0;
    font-size: 13px;
    font-weight: 500;
    color: var(--vscode-foreground);
}

.placeholder-meta {
    display: flex;
    flex-direction: column;
    gap: 2px;
    margin-bottom: 8px;
}

.placeholder-meta span {
    font-size: 11px;
    color: var(--vscode-descriptionForeground);
}

.placeholder-meta .file-type {
    font-size: 9px;
    background: var(--vscode-badge-background);
    color: var(--vscode-badge-foreground);
    padding: 2px 6px;
    border-radius: 10px;
    font-weight: 500;
    text-transform: uppercase;
}

.placeholder-hint {
    font-size: 10px;
    color: var(--vscode-descriptionForeground);
    font-style: italic;
    opacity: 0.8;
}

/* Frame Loading & Error Overlays */
.frame-loading-overlay,
.frame-error-overlay,
.frame-drag-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 10;
}

.frame-loading-overlay,
.frame-error-overlay {
    display: flex;
    justify-content: center;
    align-items: center;
    backdrop-filter: blur(2px);
}

.frame-loading-overlay,
.frame-error-overlay {
    background: rgba(255, 255, 255, 0.9);
}

.frame-drag-overlay {
    position: absolute;
    top: 0;
    left: 0;
    background: transparent;
    cursor: grab;
    z-index: 5; /* Lower than loading/error overlays but higher than iframe */
}

.design-frame.dragging .frame-drag-overlay {
    cursor: grabbing;
}

.drag-ready-hint {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: var(--vscode-editor-background);
    border: 2px solid var(--vscode-focusBorder);
    border-radius: 8px;
    padding: 8px 12px;
    text-align: center;
    color: var(--vscode-foreground);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    opacity: 0;
    animation: dragHint 0.3s ease-out forwards;
    pointer-events: none;
}

.drag-ready-hint span {
    font-size: 16px;
    display: block;
    margin-bottom: 4px;
}

.drag-ready-hint p {
    margin: 0;
    font-size: 10px;
    font-weight: 500;
}

@keyframes dragHint {
    0% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.8);
    }
    100% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

.design-frame:hover .frame-drag-overlay {
    cursor: grabbing;
}

.frame-loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    color: var(--vscode-foreground);
}

.font-sample {
    padding: 12px;
    background: var(--vscode-input-background);
    border: 1px solid var(--vscode-input-border);
}

.frame-error-content span {
    font-size: 24px;
    display: block;
    margin-bottom: 8px;
}

.frame-error-content p {
    margin: 0 0 4px 0;
    font-size: 12px;
    font-weight: 500;
}

.font-sample.body {
    font-size: 1rem;
}

.suggestion-btn {
    background: var(--vscode-button-secondaryBackground);
    color: var(--vscode-button-secondaryForeground);
    border: 1px solid var(--vscode-input-border);
    padding: 8px 12px;
    border-radius: 20px;
    cursor: pointer;
    font-size: 0.8rem;
    font-weight: 500;
    transition: all 0.2s ease;
    white-space: nowrap;
}

.suggestion-btn:hover:not(:disabled) {
    background: var(--vscode-button-secondaryHoverBackground);
    border-color: var(--vscode-focusBorder);
    transform: translateY(-1px);
}

/* Loading States */
.canvas-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    background: var(--vscode-editor-background);
}

.loading-spinner {
    text-align: center;
    color: var(--vscode-foreground);
}

.spinner {
    width: 32px;
    height: 32px;
    border: 3px solid var(--vscode-input-border);
    border-top: 3px solid var(--vscode-button-background);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 16px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Error States */
.canvas-error {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    background: var(--vscode-editor-background);
}

.error-message {
    text-align: center;
    padding: 32px;
    background: var(--vscode-sideBar-background);
    border-radius: 8px;
    border: 1px solid var(--vscode-panel-border);
    max-width: 400px;
}

.error-message h3 {
    margin: 0 0 12px 0;
    color: var(--vscode-errorForeground);
}

.error-message p {
    margin: 0 0 20px 0;
    color: var(--vscode-foreground);
}

.error-message button {
    background: var(--vscode-button-background);
    color: var(--vscode-button-foreground);
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
}

/* Empty States */
.canvas-empty {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    background: var(--vscode-editor-background);
}

.empty-state {
    text-align: center;
    color: var(--vscode-descriptionForeground);
}

.empty-state h3 {
    margin: 0 0 12px 0;
    color: var(--vscode-foreground);
}

.empty-state code {
    background: var(--vscode-textCodeBlock-background);
    padding: 2px 6px;
    border-radius: 3px;
    font-family: var(--vscode-editor-font-family);
}

/* Design Panel Styles */
.design-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.tab-nav {
    display: flex;
    background: var(--vscode-sideBar-background);
    border-bottom: 1px solid var(--vscode-panel-border);
}

.tab {
    flex: 1;
    padding: 12px 16px;
    background: transparent;
    border: none;
    color: var(--vscode-foreground);
    cursor: pointer;
    font-size: 0.9rem;
    transition: background-color 0.2s ease;
}

.tab:hover {
    background: var(--vscode-list-hoverBackground);
}

.tab.active {
    background: var(--vscode-tab-activeBackground);
    border-bottom: 2px solid var(--vscode-button-background);
}

.tab-content {
    flex: 1;
    padding: 24px;
    overflow-y: auto;
}

.colors-panel h3,
.typography-panel h3,
.components-panel h3 {
    margin: 0 0 20px 0;
    color: var(--vscode-foreground);
}

.color-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 16px;
}

.color-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: var(--vscode-input-background);
    border: 1px solid var(--vscode-input-border);
    border-radius: 6px;
}

.color-swatch {
    width: 40px;
    height: 40px;
    border-radius: 4px;
    border: 1px solid var(--vscode-input-border);
}

.color-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.color-name {
    font-weight: 500;
    color: var(--vscode-foreground);
}

.color-value {
    font-size: 0.85rem;
    color: var(--vscode-descriptionForeground);
    font-family: var(--vscode-editor-font-family);
}

.font-samples {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.font-sample {
    padding: 12px;
    background: var(--vscode-input-background);
    border: 1px solid var(--vscode-input-border);
    border-radius: 6px;
}

.font-sample.h1 {
    font-size: 2rem;
    font-weight: 700;
}

.font-sample.h2 {
    font-size: 1.5rem;
    font-weight: 600;
}

.font-sample.body {
    font-size: 1rem;
}

.font-sample.caption {
    font-size: 0.85rem;
    opacity: 0.8;
}

.actions {
    padding: 16px 24px;
    border-top: 1px solid var(--vscode-panel-border);
    background: var(--vscode-sideBar-background);
}

.export-btn {
    width: 100%;
    padding: 12px 16px;
    background: var(--vscode-button-background);
    color: var(--vscode-button-foreground);
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.95rem;
    font-weight: 500;
    transition: background-color 0.2s ease;
}

.export-btn:hover {
    background: var(--vscode-button-hoverBackground);
}

.export-btn:active {
    background: var(--vscode-button-activeBackground);
}

/* Canvas View Styles */
.canvas-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background: var(--vscode-editor-background);
    position: relative;
}

.canvas-toolbar {
    position: absolute;
    top: 16px;
    left: 16px;
    z-index: 1000;
    
    display: flex;
    align-items: stretch;
    background: var(--vscode-titleBar-activeBackground);
    border: 1px solid var(--vscode-titleBar-border, var(--vscode-contrastBorder));
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.16);
    backdrop-filter: blur(8px);
    font-size: 12px;
    min-height: 32px;
    max-width: calc(100vw - 32px);
    overflow: hidden;
    transition: all 0.2s ease;
}

.canvas-toolbar:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    border-color: var(--vscode-focusBorder, var(--vscode-titleBar-border));
}

.toolbar-section {
    display: flex;
    align-items: center;
    padding: 0 12px;
    border-right: 1px solid var(--vscode-titleBar-border, var(--vscode-contrastBorder));
    position: relative;
    min-height: 32px;
}

.toolbar-section:last-child {
    border-right: none;
}

.toolbar-section:hover {
    background: var(--vscode-titleBar-hoverBackground, rgba(255, 255, 255, 0.05));
}

.control-group {
    display: flex;
    align-items: center;
    gap: 2px;
}

/* Toolbar Buttons */
.toolbar-btn {
    background: transparent;
    border: none;
    color: var(--vscode-titleBar-activeForeground);
    padding: 4px 6px;
    border-radius: 3px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 24px;
    height: 24px;
    transition: all 0.15s ease;
    font-size: 12px;
}

.toolbar-btn:hover {
    background: var(--vscode-toolbar-hoverBackground, rgba(255, 255, 255, 0.1));
    color: var(--vscode-titleBar-activeForeground);
}

.toolbar-btn:active {
    background: var(--vscode-toolbar-activeBackground, rgba(255, 255, 255, 0.15));
}

.toolbar-btn:disabled {
    opacity: 0.4;
    cursor: not-allowed;
    background: transparent;
}

.toolbar-btn.active {
    background: var(--vscode-button-background);
    color: var(--vscode-button-foreground);
}

.toolbar-btn.active:hover {
    background: var(--vscode-button-hoverBackground);
}

/* Specific Button Types */
.zoom-btn {
    width: 28px;
}

.layout-toggle {
    display: flex;
    background: var(--vscode-dropdown-background);
    border: 1px solid var(--vscode-dropdown-border);
    border-radius: 4px;
    overflow: hidden;
    margin-left: 6px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Ensure no individual button radius */
.layout-toggle .toggle-btn {
    border-radius: 0 !important;
}

.layout-toggle .toggle-btn:first-child {
    border-radius: 0 !important;
}

.layout-toggle .toggle-btn:last-child {
    border-radius: 0 !important;
}

.toggle-btn {
    background: transparent;
    border: none;
    border-radius: 0 !important;
    color: var(--vscode-dropdown-foreground);
    padding: 5px 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    min-width: 32px;
    height: 26px;
    transition: all 0.2s ease;
    border-right: 1px solid var(--vscode-dropdown-border);
    position: relative;
}

.toggle-btn:last-child {
    border-right: none;
}

.toggle-btn:hover {
    background: var(--vscode-list-hoverBackground);
    color: var(--vscode-list-hoverForeground);
    transform: translateY(-1px);
}

.toggle-btn.active {
    background: var(--vscode-button-background);
    color: var(--vscode-button-foreground);
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

.toggle-btn.active:hover {
    background: var(--vscode-button-hoverBackground);
    transform: none;
}

/* Enhanced icon styling for layout buttons */
.toggle-btn svg {
    transition: transform 0.2s ease;
}

.toggle-btn:hover svg {
    transform: scale(1.1);
}

.toggle-btn.active svg {
    transform: scale(1.05);
}

/* Toolbar Dividers and Displays */
.toolbar-divider {
    width: 1px;
    height: 16px;
    background: var(--vscode-titleBar-border, var(--vscode-contrastBorder));
    margin: 0 4px;
}

.zoom-display {
    display: flex;
    align-items: center;
    padding: 0 6px;
    margin: 0 2px;
}

.zoom-value {
    font-size: 11px;
    font-weight: 500;
    color: var(--vscode-titleBar-activeForeground);
    min-width: 32px;
    text-align: center;
}

/* Viewport Controls - Segmented Control Style */
.viewport-selector {
    display: flex;
    background: var(--vscode-dropdown-background);
    border: 1px solid var(--vscode-dropdown-border);
    border-radius: 4px;
    overflow: hidden;
    margin-left: 6px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Ensure no individual button radius */
.viewport-selector .viewport-btn {
    border-radius: 0 !important;
}

.viewport-selector .viewport-btn:first-child {
    border-radius: 0 !important;
}

.viewport-selector .viewport-btn:last-child {
    border-radius: 0 !important;
}

.viewport-btn {
    background: transparent;
    border: none;
    border-radius: 0 !important;
    color: var(--vscode-dropdown-foreground);
    padding: 5px 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    min-width: 32px;
    height: 26px;
    transition: all 0.2s ease;
    border-right: 1px solid var(--vscode-dropdown-border);
    position: relative;
}

.viewport-btn:last-child {
    border-right: none;
}

.viewport-btn:hover:not(:disabled) {
    background: var(--vscode-list-hoverBackground);
    color: var(--vscode-list-hoverForeground);
}

.viewport-btn:disabled {
    opacity: 0.3;
    cursor: not-allowed;
    background: var(--vscode-input-background);
    color: var(--vscode-disabledForeground);
}

.viewport-btn.active {
    background: var(--vscode-button-background);
    color: var(--vscode-button-foreground);
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

.viewport-btn.active:hover {
    background: var(--vscode-button-hoverBackground);
}

/* Enhanced icon styling for viewport buttons */
.viewport-btn svg {
    transition: transform 0.2s ease;
}

.viewport-btn:hover:not(:disabled) svg {
    transform: scale(1.1);
}

.viewport-btn.active svg {
    transform: scale(1.05);
}

/* Additional Button Styles */
.connection-btn {
    margin-left: 6px;
}

.viewport-mode-btn {
    margin-right: 6px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .canvas-toolbar {
        left: 8px;
        top: 8px;
        flex-wrap: wrap;
        max-width: calc(100vw - 16px);
    }
    
    .toolbar-section {
        padding: 0 8px;
    }
}

/* Floating Action Buttons */
.floating-action-buttons {
    position: absolute;
    top: 8px;
    right: -190px;
    display: flex;
    flex-direction: column;
    gap: 6px;
    z-index: 200;
    animation: fadeInFromRight 0.3s ease forwards;
}

.floating-action-btn {
    background: white;
    color: #333;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    padding: 10px 14px;
    font-size: 0.8rem;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
    transition: all 0.2s ease;
    white-space: nowrap;
    min-width: 150px;
    justify-content: flex-start;
}

.floating-action-btn:hover {
    background: #f8f9fa;
    transform: translateY(-1px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    border-color: rgba(0, 0, 0, 0.2);
}

.floating-action-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.floating-action-btn .btn-icon {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
    color: #666;
}

.floating-action-btn:hover .btn-icon {
    color: #333;
}

.floating-action-btn .btn-text {
    font-size: 0.8rem;
    font-weight: 500;
    color: #333;
}

@keyframes fadeInFromRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Copied Notification */
.copied-notification {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: var(--vscode-notifications-background);
    color: var(--vscode-notifications-foreground);
    border: 1px solid var(--vscode-notifications-border);
    border-radius: 6px;
    padding: 12px 16px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.85rem;
    font-weight: 500;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    z-index: 300;
    animation: fadeInOut 2s ease forwards;
    pointer-events: none;
}

.copied-notification .copied-icon {
    font-size: 1rem;
}

.copied-notification .copied-text {
    white-space: nowrap;
}

@keyframes fadeInOut {
    0% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.8);
    }
    15% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
    85% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
    100% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.8);
    }
}

/* Copy Prompt Dropdown */
.copy-prompt-dropdown {
    position: relative;
    display: inline-block;
}

.copy-prompt-main-btn {
    position: relative;
    padding-right: 40px !important; /* Make room for dropdown arrow */
}

.copy-prompt-main-btn.success {
    background: #1a1a1a !important;
    color: white !important;
    border-color: #1a1a1a !important;
}

.copy-prompt-main-btn.success .btn-icon,
.copy-prompt-main-btn.success .dropdown-arrow {
    color: white !important;
}

.copy-prompt-main-btn.success .btn-text {
    color: white !important;
}

.copy-prompt-main-btn.success .btn-icon {
    display: none;
}

.copy-prompt-main-btn.success::before {
    content: "✓";
    font-size: 16px;
    margin-right: 8px;
    color: white !important;
}

.dropdown-arrow {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    width: 12px;
    height: 12px;
    color: #666;
    transition: transform 0.2s ease;
}

.copy-prompt-main-btn:hover .dropdown-arrow {
    color: #333;
}

.copy-dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    z-index: 300;
    margin-top: 4px;
    overflow: hidden;
    animation: dropdownFadeIn 0.2s ease forwards;
}

@keyframes dropdownFadeIn {
    from {
        opacity: 0;
        transform: translateY(-8px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.copy-dropdown-item {
    width: 100%;
    background: none;
    border: none;
    padding: 10px 14px;
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    font-size: 0.8rem;
    font-weight: 500;
    color: #333;
    text-align: left;
    transition: background-color 0.2s ease;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.copy-dropdown-item:last-child {
    border-bottom: none;
}

.copy-dropdown-item:hover {
    background: #f8f9fa;
}

.copy-dropdown-item:active {
    background: #e9ecef;
}

.platform-logo {
    width: 20px;
    height: 20px;
    object-fit: contain;
    flex-shrink: 0;
}

.copy-dropdown-item span {
    flex: 1;
    white-space: nowrap;
}