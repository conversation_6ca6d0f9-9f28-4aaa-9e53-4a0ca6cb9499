name: Publish Extension

on:
  push:
    tags:
      - 'v*'  # e.g., v0.1.9

jobs:
  publish:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'

      - name: Install dependencies
        run: npm ci

      - name: Install CLI tools
        run: |
          npm install -g @vscode/vsce ovsx

      - name: Publish to Visual Studio Marketplace
        run: vsce publish --pat ${{ secrets.VSCE_TOKEN }}

      - name: Publish to Open VSX
        run: ovsx publish -p ${{ secrets.OPEN_VSX_TOKEN }}

