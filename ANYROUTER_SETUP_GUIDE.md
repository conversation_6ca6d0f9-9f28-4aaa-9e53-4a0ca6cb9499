# Anyrouter.top API 配置指南

## 🔍 问题诊断结果

经过详细测试，我们发现了以下问题：

### 1. 主域名被拦截
- `https://anyrouter.top` 被 WAF (Web Application Firewall) 拦截
- 返回 HTML 错误页面，显示 "denied by http_custom"

### 2. 备用端点可以连接
- `https://pmpjfbhq.cn-nb1.rainapp.top` 可以正常连接
- 返回有效的 JSON 响应

### 3. API 密钥或认证问题
- 所有请求都返回 "invalid claude code request" 错误
- 可能的原因：
  - API 密钥无效或过期
  - 需要特殊的 Claude Code 客户端认证
  - 账户未激活或无余额

## 🛠️ 已实施的修复

### 1. 更新了代码配置
已将 `src/services/customAgentService.ts` 中的配置更新为：

```typescript
const anthropic = createAnthropic({
    apiKey: anthropicKey,
    baseURL: "https://pmpjfbhq.cn-nb1.rainapp.top",
    headers: {
        "User-Agent": "Claude-Code/1.0.31 (Node.js)",
        "anthropic-version": "2023-06-01",
        "Accept": "application/json"
    }
});
```

### 2. 配置了 API 密钥
已将提供的 API 密钥配置到 VS Code 设置中：
- `superdesign.anthropicApiKey`: `sk-5UeqYtwdz9d5iaY5nmUXXxHwhz6g8H1saFDMiURfrVE1TRRA`
- `superdesign.aiModelProvider`: `anthropic`

## 🚨 需要用户验证的事项

### 1. 验证 API 密钥
请确认以下事项：

1. **访问 anyrouter.top 网站**
   - 登录您的账户
   - 检查 API 密钥是否正确
   - 确认账户有足够的余额/积分

2. **检查 API 密钥格式**
   - anyrouter.top 的 API 密钥可能与标准 Anthropic 密钥格式不同
   - 确认密钥是从 anyrouter.top 网站生成的，而不是 Anthropic 官网

3. **验证账户状态**
   - 确认账户已激活
   - 检查是否有使用限制或地区限制

### 2. 测试连接
运行以下命令测试连接：

```bash
node test-backup-endpoint.js
```

如果仍然返回 "invalid claude code request" 错误，请：

1. **重新生成 API 密钥**
   - 在 anyrouter.top 网站上删除当前密钥
   - 生成新的 API 密钥
   - 更新配置

2. **联系 anyrouter.top 支持**
   - 询问 Claude Code 集成的具体要求
   - 确认 API 密钥格式和认证方式

## 🔄 替代方案

如果 anyrouter.top 无法正常工作，建议考虑以下替代方案：

### 1. 使用官方 Anthropic API
```typescript
const anthropic = createAnthropic({
    apiKey: 'your-official-anthropic-key',
    baseURL: "https://api.anthropic.com"
});
```

### 2. 使用其他代理服务
- OpenRouter (https://openrouter.ai/)
- 其他 Claude API 代理服务

### 3. 配置 OpenRouter
如果选择 OpenRouter，可以这样配置：

```typescript
const openrouter = createOpenRouter({
    apiKey: 'your-openrouter-key'
});
const model = openrouter.chat('anthropic/claude-3-5-sonnet-20241022');
```

## 📝 下一步操作

1. **验证 anyrouter.top 账户和 API 密钥**
2. **如果密钥有效，重启 VS Code 测试扩展**
3. **如果仍有问题，考虑使用替代方案**
4. **联系 anyrouter.top 技术支持获取帮助**

## 🧪 测试脚本

项目中包含以下测试脚本：

- `test-claude-code-simulation.js` - 测试 Claude Code 模拟
- `test-backup-endpoint.js` - 测试备用端点
- `test-claude-code-format.js` - 测试不同请求格式
- `configure-anyrouter-api.js` - 配置 API 密钥

运行这些脚本可以帮助诊断具体问题。

## 📞 获取帮助

如果问题持续存在，请：

1. 检查 anyrouter.top 网站的文档和 FAQ
2. 联系 anyrouter.top 客服支持
3. 考虑在相关技术社区寻求帮助

---

**注意**: 本指南基于当前的测试结果。anyrouter.top 的服务可能会有变化，请以官方文档为准。
