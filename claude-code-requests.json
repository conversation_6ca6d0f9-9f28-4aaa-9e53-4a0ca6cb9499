[{"id": "req_1753469742135_n9opszbhu", "timestamp": "2025-07-25T18:55:42.135Z", "method": "POST", "url": "/v1/messages?beta=true", "headers": ["host", "connection", "accept", "anthropic-beta", "anthropic-dangerous-direct-browser-access", "anthropic-version", "authorization", "content-type", "user-agent", "x-app", "x-stainless-arch", "x-stainless-lang", "x-stainless-os", "x-stainless-package-version", "x-stainless-retry-count", "x-stainless-runtime", "x-stainless-runtime-version", "x-stainless-timeout", "accept-language", "sec-fetch-mode", "accept-encoding", "content-length"], "hasBody": true, "filename": "claude-request-req_1753469742135_n9opszbhu.json"}, {"id": "req_1753469742195_tqr2sj3vg", "timestamp": "2025-07-25T18:55:42.195Z", "method": "POST", "url": "/v1/messages?beta=true", "headers": ["host", "connection", "accept", "anthropic-beta", "anthropic-dangerous-direct-browser-access", "anthropic-version", "authorization", "content-type", "user-agent", "x-app", "x-stainless-arch", "x-stainless-helper-method", "x-stainless-lang", "x-stainless-os", "x-stainless-package-version", "x-stainless-retry-count", "x-stainless-runtime", "x-stainless-runtime-version", "x-stainless-timeout", "accept-language", "sec-fetch-mode", "accept-encoding", "content-length"], "hasBody": true, "filename": "claude-request-req_1753469742195_tqr2sj3vg.json"}, {"id": "req_1753469746200_uiospzx0b", "timestamp": "2025-07-25T18:55:46.200Z", "method": "POST", "url": "/v1/messages?beta=true", "headers": ["host", "connection", "accept", "anthropic-beta", "anthropic-dangerous-direct-browser-access", "anthropic-version", "authorization", "content-type", "user-agent", "x-app", "x-stainless-arch", "x-stainless-helper-method", "x-stainless-lang", "x-stainless-os", "x-stainless-package-version", "x-stainless-retry-count", "x-stainless-runtime", "x-stainless-runtime-version", "x-stainless-timeout", "accept-language", "sec-fetch-mode", "accept-encoding", "content-length"], "hasBody": true, "filename": "claude-request-req_1753469746200_uiospzx0b.json"}, {"id": "req_1753469746244_fb1qd2083", "timestamp": "2025-07-25T18:55:46.244Z", "method": "POST", "url": "/v1/messages?beta=true", "headers": ["host", "connection", "accept", "anthropic-beta", "anthropic-dangerous-direct-browser-access", "anthropic-version", "authorization", "content-type", "user-agent", "x-app", "x-stainless-arch", "x-stainless-helper-method", "x-stainless-lang", "x-stainless-os", "x-stainless-package-version", "x-stainless-retry-count", "x-stainless-runtime", "x-stainless-runtime-version", "x-stainless-timeout", "accept-language", "sec-fetch-mode", "accept-encoding", "content-length"], "hasBody": true, "filename": "claude-request-req_1753469746244_fb1qd2083.json"}, {"id": "req_1753469746283_neg88zkt2", "timestamp": "2025-07-25T18:55:46.283Z", "method": "POST", "url": "/v1/messages?beta=true", "headers": ["host", "connection", "accept", "anthropic-beta", "anthropic-dangerous-direct-browser-access", "anthropic-version", "authorization", "content-type", "user-agent", "x-app", "x-stainless-arch", "x-stainless-lang", "x-stainless-os", "x-stainless-package-version", "x-stainless-retry-count", "x-stainless-runtime", "x-stainless-runtime-version", "x-stainless-timeout", "accept-language", "sec-fetch-mode", "accept-encoding", "content-length"], "hasBody": true, "filename": "claude-request-req_1753469746283_neg88zkt2.json"}]