/* Base chat interface styles */
.chat-interface {
    display: flex;
    flex-direction: column;
    height: 100vh;
    width: 100%;
    background: var(--vscode-sideBar-background);
    color: var(--vscode-sideBar-foreground);
    position: relative;
    overflow: hidden;
}

.chat-interface--panel {
    max-width: 100%;
    background: var(--vscode-panel-background);
    color: var(--vscode-panel-foreground);
}

.chat-interface--sidebar {
    font-size: 12px;
    background: var(--vscode-sideBar-background);
    color: var(--vscode-sideBar-foreground);
}

/* Header styles (panel only) */
.chat-header {
    padding: 0;
    border-bottom: 1px solid var(--vscode-panel-border);
    margin-bottom: 0;
    position: relative;
}

.chat-header h2 {
    margin: 0 0 8px 0;
    font-size: 18px;
    font-weight: 600;
}

.chat-header p {
    margin: 0;
    color: var(--vscode-descriptionForeground);
    font-size: 14px;
}

/* New Conversation Button */
.new-conversation-btn {
    position: absolute;
    top: 0;
    right: 0;
    background: transparent;
    border: none;
    color: var(--vscode-descriptionForeground);
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    opacity: 0.7;
}

.new-conversation-btn:hover {
    background: var(--vscode-list-hoverBackground);
    color: var(--vscode-foreground);
    opacity: 1;
}

.new-conversation-btn:active {
    background: var(--vscode-list-activeSelectionBackground);
    transform: scale(0.95);
}

.new-conversation-btn:disabled {
    opacity: 0.4;
    cursor: not-allowed;
    background: transparent;
    transform: none;
}

/* Chat container */
.chat-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: relative;
}

.chat-history {
    flex: 1;
    overflow-y: auto;
    padding: 0 12px 8px 12px;
    margin-bottom: 0;
    scroll-behavior: smooth;
}

.chat-history::-webkit-scrollbar {
    width: 6px;
}

.chat-history::-webkit-scrollbar-track {
    background: transparent;
}

.chat-history::-webkit-scrollbar-thumb {
    background: var(--vscode-scrollbarSlider-background);
    border-radius: 3px;
}

.chat-history::-webkit-scrollbar-thumb:hover {
    background: var(--vscode-scrollbarSlider-hoverBackground);
}

/* Chat messages */
.chat-message {
    margin-bottom: 16px;
    padding: 14px 16px;
    border-radius: 12px;
    line-height: 1.5;
    border: 1px solid transparent;
    transition: all 0.2s ease;
    position: relative;
}

.chat-message--panel {
    padding: 18px 20px;
    margin-bottom: 20px;
    font-size: 14px;
    border-radius: 16px;
}

.chat-message--sidebar {
    padding: 10px 12px;
    margin-bottom: 12px;
    font-size: 12px;
    border-radius: 10px;
}

/* User message specific overrides for compact styling */
.chat-message--user.chat-message--panel {
    padding: 12px 14px;
    margin-bottom: 16px;
    border-radius: 8px;
}

.chat-message--user.chat-message--sidebar {
    padding: 8px 10px;
    margin-bottom: 10px;
    border-radius: 6px;
}

.chat-message--user {
    background: var(--vscode-input-background);
    border: 1px solid var(--vscode-input-border);
    margin-left: 0;
    margin-right: 0;
    max-width: 100%;
    padding: 10px 12px;
    border-radius: 8px;
    margin-bottom: 12px;
}

/* SDK User messages (from Claude Code SDK) */
.chat-message--user-sdk {
    background: var(--vscode-input-background);
    border: 1px solid var(--vscode-textLink-foreground);
    border-left: 3px solid var(--vscode-textLink-foreground);
    margin-left: 0;
    margin-right: 0;
    max-width: 100%;
    padding: 10px 12px;
    border-radius: 8px;
    margin-bottom: 12px;
    opacity: 0.9;
}

.chat-message--assistant {
    background: transparent;
    border: none;
    border-left: none;
    margin-left: 0;
    margin-right: 0;
    max-width: 100%;
    padding-left: 0;
    border-radius: 0;
}

/* Result messages */
.chat-message--result {
    background: var(--vscode-editorWidget-background);
    border: 1px solid var(--vscode-editorWidget-border);
    border-left: 3px solid var(--vscode-charts-green);
    margin-left: 0;
    margin-right: 0;
    max-width: 100%;
    padding: 10px 12px;
    border-radius: 8px;
    margin-bottom: 12px;
    font-family: var(--vscode-editor-font-family);
    font-size: 13px;
}

/* Error result messages */
.chat-message--result-error {
    background: var(--vscode-inputValidation-errorBackground);
    border: 1px solid var(--vscode-inputValidation-errorBorder);
    border-left: 3px solid var(--vscode-errorForeground);
    margin-left: 0;
    margin-right: 0;
    max-width: 100%;
    padding: 10px 12px;
    border-radius: 8px;
    margin-bottom: 12px;
    font-family: var(--vscode-editor-font-family);
    font-size: 13px;
}

.chat-message__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 8px;
    margin-bottom: 8px;
    font-weight: 600;
    opacity: 0.8;
    font-size: 11px;
}

.chat-message--sidebar .chat-message__header {
    margin-bottom: 6px;
    font-size: 10px;
}

.chat-message__label {
    font-size: 11px;
    color: var(--vscode-descriptionForeground);
    font-weight: 500;
}

/* Metadata display */
.chat-message__metadata {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 10px;
    color: var(--vscode-descriptionForeground);
    opacity: 0.7;
}

.metadata-item {
    background: var(--vscode-badge-background);
    color: var(--vscode-badge-foreground);
    padding: 2px 4px;
    border-radius: 3px;
    font-size: 9px;
    font-weight: 500;
}

.chat-message__content {
    white-space: pre-line;
    word-wrap: break-word;
    line-height: 1.3;
    color: var(--vscode-editor-foreground);
}

/* Override white-space for markdown content to prevent excessive spacing */
.chat-message__content .markdown-content {
    white-space: normal;
}

.chat-message--user .chat-message__content {
    line-height: 1.3;
    font-size: 13px;
}

.chat-message--user:hover {
    border-color: var(--vscode-input-border);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Typing indicator */
.typing .typing-indicator {
    animation: typing 1.4s infinite ease-in-out;
    color: var(--vscode-descriptionForeground);
    font-style: italic;
}

.typing .typing-indicator::before {
    content: '';
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--vscode-textLink-foreground);
    margin-right: 8px;
    animation: pulse 1.5s infinite ease-in-out;
}

@keyframes typing {
    0%, 80%, 100% { opacity: 0.3; }
    40% { opacity: 1; }
}

@keyframes pulse {
    0%, 100% { opacity: 0.4; transform: scale(0.8); }
    50% { opacity: 1; transform: scale(1.1); }
}

/* Generating content layout */
.generating-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 6px;
    line-height: 1.3;
    margin-top: 4px;
    padding: 4px 0;
    margin-left: 0;
    margin-right: 0;
    padding-left: 0;
    padding-right: 0;
}

.generating-text {
    color: var(--vscode-descriptionForeground);
    font-size: 11px;
    flex: 1;
    animation: generating-pulse 1.8s ease-in-out infinite;
    opacity: 0.8;
}

.generating-text::after {
    content: '';
    animation: generating-dots 1.5s ease-in-out infinite;
    margin-left: 2px;
}

@keyframes generating-pulse {
    0%, 100% { opacity: 0.6; }
    50% { opacity: 1; }
}

@keyframes generating-dots {
    0%, 20% { content: ''; }
    25%, 45% { content: '.'; }
    50%, 70% { content: '..'; }
    75%, 100% { content: '...'; }
}

.generating-stop-btn {
    background: transparent;
    border: 1px solid var(--vscode-input-border);
    color: var(--vscode-descriptionForeground);
    font-size: 10px;
    cursor: pointer;
    padding: 2px 6px;
    font-family: inherit;
    transition: all 0.2s ease;
    border-radius: 4px;
    font-weight: normal;
}

.generating-stop-btn:hover {
    background: var(--vscode-list-hoverBackground);
    color: var(--vscode-foreground);
    border-color: var(--vscode-focusBorder);
}

/* Responsive generating content for different layouts - simplified */
.chat-message--panel .generating-content,
.chat-message--sidebar .generating-content {
    margin-left: 0;
    margin-right: 0;
    padding-left: 0;
    padding-right: 0;
}



/* Chat placeholder */
.chat-placeholder {
    text-align: center;
    color: var(--vscode-descriptionForeground);
    padding: 0 20px 20px 20px;
}

.chat-placeholder--panel {
    padding: 0 20px 32px 20px;
    font-size: 14px;
}

.chat-placeholder--sidebar {
    padding: 0 8px 16px 8px;
    font-size: 11px;
}

.chat-placeholder__features {
    margin-top: 16px;
}

.chat-placeholder__features ul {
    text-align: left;
    max-width: 300px;
    margin: 12px auto;
}

.chat-placeholder__features li {
    margin: 8px 0;
}

/* Empty state styles */
.chat-placeholder__content {
    margin-top: 20px;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

.empty-state-message {
    display: flex;
    flex-direction: column;
    gap: 16px;
    align-items: center;
    text-align: center;
}

.empty-state-message p {
    margin: 0;
    line-height: 1.5;
    color: var(--vscode-foreground);
}

.empty-state-message strong {
    color: var(--vscode-textLink-foreground);
    font-weight: 600;
}

.empty-state-message kbd {
    background: var(--vscode-keybindingLabel-background);
    border: 1px solid var(--vscode-keybindingLabel-border);
    border-bottom: 2px solid var(--vscode-keybindingLabel-bottomBorder);
    color: var(--vscode-keybindingLabel-foreground);
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    font-family: var(--vscode-editor-font-family);
    white-space: nowrap;
}

.empty-state-message code {
    background: var(--vscode-textCodeBlock-background);
    color: var(--vscode-textPreformat-foreground);
    padding: 2px 4px;
    border-radius: 3px;
    font-size: 12px;
    font-family: var(--vscode-editor-font-family);
}

.empty-state-divider {
    color: var(--vscode-descriptionForeground);
    font-weight: 500;
    font-size: 13px;
    margin: 8px 0;
    position: relative;
}

.empty-state-divider::before,
.empty-state-divider::after {
    content: '';
    position: absolute;
    top: 50%;
    width: 40px;
    height: 1px;
    background: var(--vscode-separator-foreground);
    opacity: 0.3;
}

.empty-state-divider::before {
    right: calc(100% + 10px);
}

.empty-state-divider::after {
    left: calc(100% + 10px);
}

.empty-state-message em {
    color: var(--vscode-descriptionForeground);
    font-style: italic;
}

/* Sidebar layout adjustments */
.chat-placeholder--sidebar .chat-placeholder__content {
    margin-top: 16px;
    max-width: 100%;
    padding: 0 8px;
}

.chat-placeholder--sidebar .empty-state-message {
    gap: 12px;
}

.chat-placeholder--sidebar .empty-state-message p {
    font-size: 12px;
    line-height: 1.4;
}

.chat-placeholder--sidebar .empty-state-message kbd {
    font-size: 10px;
    padding: 1px 4px;
}

.chat-placeholder--sidebar .empty-state-message code {
    font-size: 10px;
    padding: 1px 3px;
}

.chat-placeholder--sidebar .empty-state-divider {
    font-size: 11px;
}

.chat-placeholder--sidebar .empty-state-divider::before,
.chat-placeholder--sidebar .empty-state-divider::after {
    width: 20px;
}

/* Responsive adjustments for empty state */
@media (max-width: 600px) {
    .chat-placeholder__content {
        max-width: 90%;
        margin-top: 16px;
    }
    
    .empty-state-message {
        gap: 12px;
    }
    
    .empty-state-message p {
        font-size: 13px;
    }
    
    .empty-state-divider::before,
    .empty-state-divider::after {
        width: 20px;
    }
}

/* Input container - Cursor style - REMOVED */

/* Add Context Button */
.add-context-btn {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 4px;
    background: transparent;
    color: var(--vscode-foreground);
    border: 1px solid var(--vscode-input-border);
    border-radius: 6px;
    padding: 4px 4px;
    margin: 6px 6px 6px 6px;
    cursor: pointer;
    font-size: 11px;
    font-family: inherit;
    transition: all 0.2s ease;
    width: fit-content;
    line-height: 1;
}

.add-context-btn:hover:not(:disabled) {
    background: var(--vscode-button-hoverBackground);
    border-color: var(--vscode-focusBorder);
}

.add-context-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.add-context-icon {
    font-weight: normal;
    font-size: 11px;
    line-height: 1;
    display: inline-block;
}

/* Message context display in user bubbles */
.message-context-display {
    display: inline-flex;
    align-items: center;
    justify-content: flex-start;
    gap: 4px;
    background: transparent;
    color: var(--vscode-foreground);
    border: 1px solid var(--vscode-input-border);
    border-radius: 4px;
    padding: 4px 8px;
    margin-bottom: 8px;
    font-size: 11px;
    font-family: inherit;
    width: fit-content;
    line-height: 1;
}

.message-context-display .context-icon {
    font-weight: normal;
    font-size: 11px;
    line-height: 1;
    display: inline-block;
}

.message-context-display .context-text {
    font-size: 11px;
    line-height: 1;
    color: var(--vscode-foreground);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 200px;
}

.message-text {
    margin: 0;
}

/* Main Input Wrapper */
.chat-input-wrapper {
    background: var(--vscode-input-background);
    border: 1px solid var(--vscode-input-border);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    transition: all 0.2s ease;
    margin: 6px 6px 12px 6px;
    margin-top: auto;
    flex-shrink: 0;
}

/* Remove focus highlight from chat input wrapper */

/* Input Controls */
.input-controls {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 4px 8px;
    gap: 6px;
}

.selectors-group {
    display: flex;
    align-items: center;
    gap: 4px;
    flex-shrink: 1;
    min-width: 0;
}

.selector-wrapper {
    position: relative;
    display: inline-block;
}

/* Selector icon styles removed - handled by ModelSelector component */

.agent-selector {
    background: transparent;
    color: var(--vscode-foreground);
    border: none;
    outline: none;
    font-size: 11px;
    font-family: inherit;
    cursor: pointer;
    padding: 2px 8px 2px 18px;
    border-radius: 4px;
    transition: background-color 0.2s ease;
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3E%3Cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 12 4-4 4 4'/%3E%3C/svg%3E");
    background-position: right 6px center;
    background-repeat: no-repeat;
    background-size: 12px;
    min-width: 120px;
    white-space: nowrap;
    font-weight: 500;
}

.agent-selector:hover {
    background: var(--vscode-list-hoverBackground);
}

/* Chat input */
.chat-input {
    padding: 6px 8px 4px 8px;
    position: relative;
}

.message-input {
    width: 100%;
    padding: 0;
    background: transparent;
    color: var(--vscode-input-foreground);
    border: none;
    outline: none;
    font-size: 14px;
    font-family: inherit;
    resize: none;
    min-height: 24px;
    max-height: 120px;
    line-height: 1.5;
    overflow-y: auto;
    box-shadow: none;
    transition: all 0.2s ease;
}

.message-input:focus {
    outline: none;
    box-shadow: none;
    border: none;
}

.message-input::selection {
    background: var(--vscode-editor-selectionBackground);
}

.message-input::placeholder {
    color: var(--vscode-input-placeholderForeground);
    font-size: 14px;
}

.chat-interface--sidebar .message-input {
    font-size: 12px;
}

.chat-interface--sidebar .message-input::placeholder {
    font-size: 12px;
}

/* Input Actions */
.input-actions {
    display: flex;
    align-items: center;
    gap: 4px;
    flex-shrink: 0;
}

.attach-btn,
.send-btn {
    width: 24px;
    height: 24px;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    flex-shrink: 0;
    position: relative;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.attach-btn {
    background: transparent;
    color: var(--vscode-descriptionForeground);
    box-shadow: none;
    opacity: 0.6;
    border-radius: 0;
}

.attach-btn:hover:not(:disabled) {
    background: var(--vscode-list-hoverBackground);
    color: var(--vscode-foreground);
    opacity: 1;
    border-radius: 50%;
}

.send-btn {
    background: var(--vscode-button-background);
    color: var(--vscode-button-foreground);
}

.send-btn:hover:not(:disabled) {
    background: var(--vscode-button-hoverBackground);
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.send-btn:disabled,
.attach-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.stop-btn {
    background: var(--vscode-input-background) !important;
    color: var(--vscode-foreground) !important;
    border: 1px solid var(--vscode-input-border) !important;
}

.stop-btn:hover:not(:disabled) {
    background: var(--vscode-list-hoverBackground) !important;
    border-color: var(--vscode-focusBorder) !important;
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.attach-btn svg {
    width: 12px;
    height: 12px;
}

.send-btn svg {
    width: 14px;
    height: 14px;
}

.chat-interface--sidebar .attach-btn,
.chat-interface--sidebar .send-btn {
    width: 28px;
    height: 28px;
    font-size: 12px;
}

/* Responsive adjustments */
@media (max-width: 480px) {
    .input-controls {
        padding: 4px 6px;
        gap: 4px;
    }
    
    .selectors-group {
        gap: 2px;
        flex-shrink: 1;
        min-width: 0;
    }
    
    .agent-selector {
        min-width: 80px;
        font-size: 10px;
        padding: 2px 6px 2px 16px;
        background-size: 10px;
        background-position: right 4px center;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3E%3Cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 12 4-4 4 4'/%3E%3C/svg%3E");
    }
    
    /* Selector icon responsive styles removed - handled by ModelSelector component */
    
    .attach-btn,
    .send-btn {
        width: 20px;
        height: 20px;
        flex-shrink: 0;
    }
    
    .attach-btn svg {
        width: 10px;
        height: 10px;
    }
    
    .send-btn svg {
        width: 12px;
        height: 12px;
    }
    
    .chat-input {
        padding: 6px 8px 4px 8px;
    }
    
    .message-input {
        font-size: 13px;
    }
}

/* Streaming cursor animation - simplified */
.streaming-cursor {
    color: var(--vscode-descriptionForeground);
    font-weight: normal;
    animation: blink 1.2s ease-in-out infinite;
    margin-left: 1px;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.3; }
}

/* Tool message styles - compact */
.tool-message {
    margin-bottom: 4px;
    padding: 6px 8px;
    border: 1px solid var(--vscode-input-border);
    border-radius: 6px;
    background: var(--vscode-input-background);
    line-height: 1.2;
    transition: all 0.2s ease;
    position: relative;
}

.tool-message:hover {
    border-color: var(--vscode-focusBorder);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.tool-message--panel {
    margin-bottom: 6px;
    padding: 8px 10px;
    border-radius: 8px;
    font-size: 12px;
}

.tool-message--sidebar {
    margin-bottom: 3px;
    padding: 6px 8px;
    border-radius: 6px;
    font-size: 11px;
}

.tool-message--success {
    border-left: 3px solid var(--vscode-charts-green);
}

.tool-message--error {
    background: var(--vscode-inputValidation-errorBackground);
    border: 1px solid var(--vscode-inputValidation-errorBorder);
    border-left: 3px solid var(--vscode-errorForeground);
}

.tool-message--loading {
    border-left: 3px solid var(--vscode-progressBar-background);
}

.tool-message--complete {
    border-left: 3px solid var(--vscode-charts-green);
}

.tool-message__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 8px;
    margin-bottom: 0;
    font-weight: 600;
    opacity: 0.8;
    font-size: 11px;
}

.chat-interface--sidebar .tool-message__header {
    margin-bottom: 0;
    font-size: 10px;
}

/* Only add bottom margin when details are showing */
.tool-message__header:has(+ .tool-message__details) {
    margin-bottom: 8px;
}

.chat-interface--sidebar .tool-message__header:has(+ .tool-message__details) {
    margin-bottom: 6px;
}

.tool-message__main {
    display: flex;
    align-items: center;
    gap: 6px;
    flex: 1;
    min-width: 0;
}

.tool-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    width: 14px;
    height: 14px;
    color: var(--vscode-descriptionForeground);
}

/* Tool icon state-specific colors */
.tool-message--success .tool-icon {
    color: var(--vscode-charts-green);
}

.tool-message--complete .tool-icon {
    color: var(--vscode-charts-green);
}

.tool-message--error .tool-icon {
    color: var(--vscode-errorForeground);
}

.tool-info {
    display: flex;
    flex-direction: column;
    gap: 0;
    flex: 1;
    min-width: 0;
}

.tool-name {
    font-weight: 500;
    color: var(--vscode-foreground);
    flex-shrink: 0;
    font-size: 11px;
    line-height: 1.2;
}

.tool-description {
    color: var(--vscode-descriptionForeground);
    font-size: 10px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    line-height: 1.2;
}

.tool-actions {
    display: flex;
    align-items: center;
    gap: 2px;
    flex-shrink: 0;
}

.tool-status {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 12px;
    height: 12px;
    flex-shrink: 0;
    color: var(--vscode-charts-green);
}

.tool-status--success {
    color: var(--vscode-charts-green);
}

.tool-status--complete {
    color: var(--vscode-charts-green);
}

.tool-status--error {
    color: var(--vscode-errorForeground);
}

.tool-expand-btn {
    background: transparent;
    border: none;
    color: var(--vscode-descriptionForeground);
    cursor: pointer;
    padding: 1px;
    border-radius: 2px;
    transition: all 0.2s ease;
    flex-shrink: 0;
    width: 14px;
    height: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.tool-expand-btn:hover {
    background: var(--vscode-toolbar-hoverBackground);
    color: var(--vscode-foreground);
}

.tool-expand-btn.expanded {
    transform: rotate(45deg);
    color: var(--vscode-foreground);
}

.tool-message__details {
    margin-top: 6px;
    padding-top: 6px;
    border-top: 1px solid var(--vscode-input-border);
}

.tool-detail {
    margin-bottom: 8px;
}

.tool-detail:last-child {
    margin-bottom: 0;
}

.tool-detail__label {
    display: block;
    font-weight: 600;
    color: var(--vscode-descriptionForeground);
    font-size: 9px;
    margin-bottom: 2px;
    text-transform: uppercase;
    letter-spacing: 0.3px;
    line-height: 1.2;
}

.tool-detail__value {
    display: block;
    color: var(--vscode-editor-foreground);
    font-size: 10px;
    line-height: 1.3;
}

.tool-detail__value--text {
    background: var(--vscode-textCodeBlock-background);
    border: 1px solid var(--vscode-widget-border);
    border-radius: 3px;
    padding: 4px 6px;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.tool-detail__value--json {
    background: var(--vscode-textCodeBlock-background);
    border: 1px solid var(--vscode-widget-border);
    border-radius: 3px;
    padding: 4px 6px;
    font-family: var(--vscode-editor-font-family);
    font-size: 9px;
    overflow-x: auto;
    max-height: 150px;
    overflow-y: auto;
}

/* Details element styling */
.tool-detail details {
    margin: 0;
}

.tool-detail details summary {
    cursor: pointer;
    padding: 2px 0;
    font-weight: 600;
    color: var(--vscode-descriptionForeground);
    font-size: 9px;
    text-transform: uppercase;
    letter-spacing: 0.3px;
    transition: color 0.2s ease;
}

.tool-detail details summary:hover {
    color: var(--vscode-foreground);
}

.tool-detail details[open] summary {
    margin-bottom: 4px;
}

.tool-detail__value--result {
    background: var(--vscode-textCodeBlock-background);
    border: 1px solid var(--vscode-widget-border);
    border-radius: 3px;
    padding: 0;
    overflow: hidden;
}

.tool-detail__value--error {
    border-color: var(--vscode-errorForeground);
    background: var(--vscode-inputValidation-errorBackground);
}

.tool-result-content {
    padding: 6px 8px;
    margin: 0;
    font-family: var(--vscode-editor-font-family);
    font-size: 9px;
    line-height: 1.3;
    white-space: pre-wrap;
    word-wrap: break-word;
    max-height: 200px;
    overflow-y: auto;
    background: transparent;
    border: none;
}

.tool-result__show-more {
    background: var(--vscode-button-background);
    color: var(--vscode-button-foreground);
    border: none;
    border-top: 1px solid var(--vscode-widget-border);
    width: 100%;
    padding: 4px 8px;
    font-size: 9px;
    cursor: pointer;
    transition: background-color 0.15s ease;
}

.tool-result__show-more:hover {
    background: var(--vscode-button-hoverBackground);
}

.tool-result__show-more:active {
    background: var(--vscode-button-background);
}

/* Tool result styles */
.tool-result {
    margin-bottom: 6px;
    border: 1px solid var(--vscode-widget-border);
    border-radius: 4px;
    background: var(--vscode-editor-background);
    overflow: hidden;
}

.tool-result--panel {
    margin-bottom: 8px;
}

.tool-result--sidebar {
    margin-bottom: 4px;
}

.tool-result--success {
    border-left: 2px solid var(--vscode-charts-green);
}

.tool-result--error {
    border-left: 2px solid var(--vscode-errorForeground);
}

.tool-result__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 4px 6px;
    cursor: pointer;
    background: transparent;
    border-bottom: 1px solid transparent;
    transition: background-color 0.15s ease;
}

.tool-result__header:hover {
    background: var(--vscode-list-hoverBackground);
}

.tool-result__main {
    display: flex;
    align-items: center;
    gap: 4px;
    flex: 1;
    min-width: 0;
}

.tool-result-icon {
    font-size: 10px;
    flex-shrink: 0;
}

.tool-result-label {
    font-weight: 600;
    color: var(--vscode-foreground);
    flex-shrink: 0;
    font-size: 9px;
}

.tool-result-meta {
    color: var(--vscode-descriptionForeground);
    font-size: 8px;
    background: var(--vscode-badge-background);
    color: var(--vscode-badge-foreground);
    padding: 1px 3px;
    border-radius: 6px;
    flex-shrink: 0;
}

.tool-result__content {
    padding: 6px 8px;
    border-top: 1px solid var(--vscode-widget-border);
    background: var(--vscode-editor-background);
}

.tool-result__text {
    background: var(--vscode-textCodeBlock-background);
    border: 1px solid var(--vscode-widget-border);
    border-radius: 3px;
    padding: 6px 8px;
    font-family: var(--vscode-editor-font-family);
    font-size: 9px;
    line-height: 1.3;
    white-space: pre-wrap;
    word-wrap: break-word;
    margin: 0;
    max-height: 250px;
    overflow-y: auto;
}

.tool-result__show-more {
    background: var(--vscode-button-background);
    color: var(--vscode-button-foreground);
    border: none;
    border-radius: 3px;
    padding: 3px 6px;
    font-size: 9px;
    cursor: pointer;
    margin-top: 4px;
    transition: background-color 0.15s ease;
}

.tool-result__show-more:hover {
    background: var(--vscode-button-hoverBackground);
}

.tool-result__show-more:active {
    background: var(--vscode-button-background);
}

.loading-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Tool group styles - compact */
.tool-group {
    margin-bottom: 8px;
    padding: 6px 8px;
    border: 1px solid var(--vscode-input-border);
    border-radius: 6px;
    background: var(--vscode-input-background);
    line-height: 1.2;
    transition: all 0.2s ease;
    position: relative;
}

.tool-group:hover {
    border-color: var(--vscode-focusBorder);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.tool-group--panel {
    margin-bottom: 10px;
    padding: 8px 10px;
    border-radius: 8px;
    font-size: 12px;
}

.tool-group--sidebar {
    margin-bottom: 6px;
    padding: 6px 8px;
    border-radius: 6px;
    font-size: 11px;
}

.tool-group--success {
    border-left: 3px solid var(--vscode-charts-green);
}

.tool-group--error {
    background: var(--vscode-inputValidation-errorBackground);
    border: 1px solid var(--vscode-inputValidation-errorBorder);
    border-left: 3px solid var(--vscode-errorForeground);
}

.tool-group--loading {
    border-left: 3px solid var(--vscode-progressBar-background);
}

.tool-group--complete {
    border-left: 3px solid var(--vscode-charts-green);
}

.tool-group__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 8px;
    margin-bottom: 0;
    font-weight: 600;
    opacity: 0.8;
    font-size: 11px;
}

.chat-interface--sidebar .tool-group__header {
    margin-bottom: 0;
    font-size: 10px;
}

/* Only add bottom margin when children are showing */
.tool-group__header:has(+ .tool-group__children) {
    margin-bottom: 8px;
}

.chat-interface--sidebar .tool-group__header:has(+ .tool-group__children) {
    margin-bottom: 6px;
}

.tool-group__main {
    display: flex;
    align-items: center;
    gap: 6px;
    flex: 1;
    min-width: 0;
}

.tool-group-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    width: 14px;
    height: 14px;
    color: var(--vscode-descriptionForeground);
}

/* Tool group icon state-specific colors */
.tool-group--success .tool-group-icon {
    color: var(--vscode-charts-green);
}

.tool-group--complete .tool-group-icon {
    color: var(--vscode-charts-green);
}

.tool-group--error .tool-group-icon {
    color: var(--vscode-errorForeground);
}

.tool-group-info {
    display: flex;
    flex-direction: column;
    gap: 0;
    flex: 1;
    min-width: 0;
}

.tool-group-name {
    font-weight: 500;
    color: var(--vscode-foreground);
    flex-shrink: 0;
    font-size: 11px;
    line-height: 1.2;
}

.tool-time-remaining {
    display: flex;
    align-items: center;
    gap: 2px;
    font-size: 10px;
    color: var(--vscode-descriptionForeground);
    font-weight: normal;
    line-height: 1.2;
}

.tool-group-actions {
    display: flex;
    align-items: center;
    gap: 2px;
    flex-shrink: 0;
}

.tool-group-count {
    background: var(--vscode-badge-background);
    color: var(--vscode-badge-foreground);
    font-size: 9px;
    padding: 1px 3px;
    border-radius: 2px;
    font-weight: 500;
    flex-shrink: 0;
}

/* Simple loading icon */
.loading-icon-simple {
    position: relative;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.loading-ring {
    width: 12px;
    height: 12px;
    border: 1px solid var(--vscode-list-inactiveSelectionBackground);
    border-top: 1px solid var(--vscode-progressBar-background);
    border-radius: 50%;
    animation: spin-simple 1s linear infinite;
}

@keyframes spin-simple {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.tool-group__children {
    margin-top: 6px;
    padding-top: 6px;
    border-top: 1px solid var(--vscode-input-border);
}

.tool-group__children .tool-message {
    margin-bottom: 6px;
    margin-left: 8px;
    border-left: 2px solid var(--vscode-input-border);
    padding-left: 6px;
    background: transparent;
    border-radius: 4px;
    padding: 4px 6px;
}

.tool-group__children .tool-message:last-child {
    margin-bottom: 0;
}

/* Context Display */
.context-display {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 4px 8px;
    background: transparent;
    border: 1px solid var(--vscode-input-border);
    border-radius: 4px;
    font-size: 11px;
    margin-left: 6px;
    margin-top: 6px;
    width: fit-content;
}

.context-icon {
    font-size: 12px;
    opacity: 0.8;
}

.context-text {
    color: var(--vscode-descriptionForeground);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 200px;
}

.context-clear-btn {
    background: none;
    border: none;
    color: var(--vscode-descriptionForeground);
    cursor: pointer;
    font-size: 14px;
    padding: 0;
    margin-left: 4px;
    opacity: 0.6;
    transition: opacity 0.2s;
}

.context-clear-btn:hover {
    opacity: 1;
}

/* Upload Progress */
.upload-progress {
    margin-left: 6px;
    margin-top: 6px;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.upload-summary {
    font-size: 11px;
    color: var(--vscode-button-background);
    font-weight: 600;
    margin-bottom: 2px;
    padding-left: 2px;
}

.uploading-item {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 4px 8px;
    background: var(--vscode-editor-hoverHighlightBackground);
    border: 1px solid var(--vscode-input-border);
    border-radius: 4px;
    font-size: 11px;
    width: fit-content;
    max-width: 300px;
}

.upload-icon {
    font-size: 12px;
    opacity: 0.8;
}

.upload-text {
    color: var(--vscode-descriptionForeground);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex: 1;
}

.upload-spinner {
    width: 12px;
    height: 12px;
    border: 1px solid var(--vscode-input-border);
    border-top: 1px solid var(--vscode-button-background);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Loading Tips */
.tool-loading-tips {
    background: var(--vscode-textCodeBlock-background);
    border: 1px solid var(--vscode-input-border);
    border-radius: 6px;
    padding: 12px;
    margin-bottom: 12px;
}

.loading-tip {
    display: flex;
    align-items: flex-start;
    gap: 8px;
}

.tip-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    color: var(--vscode-descriptionForeground);
    opacity: 0.8;
}

.tip-text {
    font-size: 12px;
    line-height: 1.4;
    color: var(--vscode-foreground);
    font-style: italic;
    animation: tipFadeIn 0.5s ease-in;
}

@keyframes tipFadeIn {
    0% { opacity: 0; transform: translateY(5px); }
    100% { opacity: 1; transform: translateY(0); }
}

/* Enhanced tool message states */
.tool-message--loading .tool-icon {
    color: var(--vscode-progressBar-background);
    animation: pulse-loading 2s ease-in-out infinite;
}

/* Enhanced tool group states */
.tool-group--loading .tool-group-icon {
    color: var(--vscode-progressBar-background);
    animation: pulse-loading 2s ease-in-out infinite;
}

/* Tool status enhancements - simplified */
.tool-status--loading {
    color: var(--vscode-descriptionForeground);
    animation: rotate 2s linear infinite;
}

@keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes pulse-loading {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 1; }
}

/* Responsive adjustments for mobile */
@media (max-width: 768px) {
    .tip-text {
        font-size: 11px;
    }
    
    .tool-group-info {
        gap: 1px;
    }
    
    .tool-time-remaining {
        font-size: 10px;
    }
}

/* Sidebar Action Bar - VS Code style action buttons */
.sidebar-action-bar {
    position: absolute;
    top: 8px;
    right: 12px;
    z-index: 100;
    display: flex;
    gap: 4px;
    align-items: center;
}

.sidebar-action-btn {
    background: transparent;
    border: none;
    color: var(--vscode-icon-foreground);
    cursor: pointer;
    padding: 2px;
    border-radius: 3px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    opacity: 0.7;
    width: 22px;
    height: 22px;
}

.sidebar-action-btn:hover:not(:disabled) {
    background: var(--vscode-toolbar-hoverBackground);
    opacity: 1;
}

.sidebar-action-btn:active {
    background: var(--vscode-toolbar-activeBackground);
}

.sidebar-action-btn:disabled {
    opacity: 0.4;
    cursor: not-allowed;
    background: transparent;
}

/* Panel Action Bar */
.chat-action-bar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 6px 12px;
    background: var(--vscode-titleBar-activeBackground);
    border-bottom: 1px solid var(--vscode-panel-border);
    min-height: 32px;
    flex-shrink: 0;
}

.open-canvas-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    background: transparent;
    border: none;
    color: var(--vscode-titleBar-activeForeground);
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.open-canvas-btn:hover {
    background: var(--vscode-titleBar-hoverBackground);
    color: var(--vscode-titleBar-hoverForeground);
}

.open-canvas-btn svg {
    flex-shrink: 0;
}

.clear-chat-icon-btn {
    background: transparent;
    border: none;
    color: var(--vscode-titleBar-activeForeground);
    cursor: pointer;
    padding: 4px;
    border-radius: 3px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    transition: all 0.2s ease;
    opacity: 0.8;
}

.clear-chat-icon-btn:hover:not(:disabled) {
    background: var(--vscode-titleBar-hoverBackground);
    color: var(--vscode-titleBar-hoverForeground);
    opacity: 1;
}

.clear-chat-icon-btn:disabled {
    opacity: 0.4;
    cursor: not-allowed;
} 

.chat-interface {
}

/* Markdown content styles */
.markdown-content {
    line-height: 1.3;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
    margin: 16px 0 8px 0;
    font-weight: 600;
    line-height: 1.2;
    color: var(--vscode-editor-foreground);
}

.markdown-content h1:first-child,
.markdown-content h2:first-child,
.markdown-content h3:first-child,
.markdown-content h4:first-child,
.markdown-content h5:first-child,
.markdown-content h6:first-child {
    margin-top: 0;
}

.markdown-content h1 {
    font-size: 1.8em;
}

.markdown-content h2 {
    font-size: 1.5em;
}

.markdown-content h3 {
    font-size: 1.25em;
}

.markdown-content h4 {
    font-size: 1.1em;
}

.markdown-content p {
    margin: 8px 0;
    line-height: 1.5;
}

.markdown-content p:first-child {
    margin-top: 0;
}

.markdown-content p:last-child {
    margin-bottom: 0;
}

.markdown-content ul,
.markdown-content ol {
    margin: 8px 0;
    padding-left: 24px;
}

.markdown-content ul:first-child,
.markdown-content ol:first-child {
    margin-top: 0;
}

.markdown-content ul:last-child,
.markdown-content ol:last-child {
    margin-bottom: 0;
}

.markdown-content li {
    margin: 2px 0;
    line-height: 1.4;
}

.markdown-content li p {
    margin: 2px 0;
}

/* Nested lists */
.markdown-content li ul,
.markdown-content li ol {
    margin: 4px 0;
    padding-left: 20px;
}

.markdown-content pre {
    background: var(--vscode-textCodeBlock-background);
    border: 1px solid var(--vscode-panel-border);
    border-radius: 6px;
    padding: 8px;
    margin: 4px 0;
    overflow-x: auto;
    font-family: var(--vscode-editor-font-family);
    font-size: 13px;
    line-height: 1.3;
}

.markdown-content pre code {
    background: transparent;
    padding: 0;
    border: none;
    border-radius: 0;
    font-size: inherit;
}

.markdown-content .inline-code {
    background: var(--vscode-textCodeBlock-background);
    border: 1px solid var(--vscode-panel-border);
    border-radius: 3px;
    padding: 2px 4px;
    font-family: var(--vscode-editor-font-family);
    font-size: 0.9em;
    color: var(--vscode-textPreformat-foreground);
}

.markdown-content a {
    color: var(--vscode-textLink-foreground);
    text-decoration: none;
}

.markdown-content a:hover {
    color: var(--vscode-textLink-activeForeground);
    text-decoration: underline;
}

.markdown-content .table-wrapper {
    overflow-x: auto;
    margin: 8px 0;
}

.markdown-content table {
    border-collapse: collapse;
    width: 100%;
    border: 1px solid var(--vscode-panel-border);
}

.markdown-content th,
.markdown-content td {
    border: 1px solid var(--vscode-panel-border);
    padding: 6px 10px;
    text-align: left;
}

.markdown-content th {
    background: var(--vscode-editorWidget-background);
    font-weight: 600;
}

.markdown-content .markdown-blockquote {
    border-left: 4px solid var(--vscode-textLink-foreground);
    margin: 8px 0;
    padding: 6px 12px;
    background: var(--vscode-textBlockQuote-background);
    font-style: italic;
    color: var(--vscode-textBlockQuote-foreground);
}

.markdown-content hr {
    border: none;
    border-top: 1px solid var(--vscode-panel-border);
    margin: 12px 0;
}

.markdown-content strong {
    font-weight: 600;
    color: var(--vscode-editor-foreground);
}

.markdown-content em {
    font-style: italic;
}

/* Syntax highlighting styles for VS Code theme compatibility */
.markdown-content .hljs {
    background: transparent;
    color: var(--vscode-editor-foreground);
}

.markdown-content .hljs-keyword,
.markdown-content .hljs-built_in {
    color: var(--vscode-symbolIcon-keywordForeground, #569cd6);
}

.markdown-content .hljs-string,
.markdown-content .hljs-title {
    color: var(--vscode-symbolIcon-stringForeground, #ce9178);
}

.markdown-content .hljs-comment {
    color: var(--vscode-symbolIcon-colorForeground, #6a9955);
    font-style: italic;
}

.markdown-content .hljs-number {
    color: var(--vscode-symbolIcon-numberForeground, #b5cea8);
}

.markdown-content .hljs-function {
    color: var(--vscode-symbolIcon-functionForeground, #dcdcaa);
}

.markdown-content .hljs-variable {
    color: var(--vscode-symbolIcon-variableForeground, #9cdcfe);
}

/* Responsive adjustments for sidebar */
.chat-interface--sidebar .markdown-content h1 {
    font-size: 1.4em;
    margin: 12px 0 6px 0;
}

.chat-interface--sidebar .markdown-content h2 {
    font-size: 1.2em;
    margin: 12px 0 6px 0;
}

.chat-interface--sidebar .markdown-content h3 {
    font-size: 1.1em;
    margin: 10px 0 4px 0;
}

.chat-interface--sidebar .markdown-content h4 {
    margin: 8px 0 4px 0;
}

.chat-interface--sidebar .markdown-content p {
    margin: 6px 0;
    line-height: 1.4;
}

.chat-interface--sidebar .markdown-content ul,
.chat-interface--sidebar .markdown-content ol {
    margin: 6px 0;
    padding-left: 20px;
}

.chat-interface--sidebar .markdown-content li {
    margin: 1px 0;
    line-height: 1.3;
}

.chat-interface--sidebar .markdown-content pre {
    padding: 6px;
    font-size: 11px;
    margin: 6px 0;
}

.chat-interface--sidebar .markdown-content .inline-code {
    font-size: 0.85em;
}

.chat-interface--sidebar .markdown-content table {
    font-size: 11px;
}

.chat-interface--sidebar .markdown-content th,
.chat-interface--sidebar .markdown-content td {
    padding: 3px 5px;
}

/* Drag & Drop Overlay */

/* Error message with action buttons */
.error-message-content {
    margin-bottom: 8px;
    line-height: 1.4;
    position: relative;
    padding-right: 24px; /* Add space for close button in sidebar mode */
    color: var(--vscode-errorForeground);
}

.error-actions {
    display: flex;
    gap: 8px;
    margin-top: 8px;
}

.error-action-btn {
    background: var(--vscode-button-background);
    color: var(--vscode-button-foreground);
    border: 1px solid var(--vscode-button-border);
    border-radius: 4px;
    padding: 6px 12px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-family: inherit;
}

.error-action-btn:hover {
    background: var(--vscode-button-hoverBackground);
    border-color: var(--vscode-button-hoverBackground);
}

.error-action-btn:active {
    background: var(--vscode-button-activeBackground);
    transform: scale(0.98);
}

.error-action-btn:focus {
    outline: 2px solid var(--vscode-focusBorder);
    outline-offset: 2px;
}

.error-close-btn {
    background: none;
    border: none;
    color: var(--vscode-descriptionForeground);
    cursor: pointer;
    font-size: 18px;
    font-weight: bold;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 3px;
    transition: all 0.15s ease;
    opacity: 0.6;
    flex-shrink: 0;
}

.error-close-btn:hover {
    background: var(--vscode-toolbar-hoverBackground);
    opacity: 1;
    color: var(--vscode-foreground);
}

.error-close-btn:active {
    background: var(--vscode-toolbar-activeBackground);
}

.error-close-btn--sidebar {
    position: absolute;
    top: 8px;
    right: 8px;
    font-size: 16px;
    width: 18px;
    height: 18px;
}

/* Panel specific styling */
.chat-interface--panel .error-actions {
    gap: 10px;
}

.chat-interface--panel .error-action-btn {
    padding: 8px 16px;
    font-size: 13px;
}

/* Theme Tool Message Styles - Added for generateTheme tool calls */
.theme-tool-message {
    margin-bottom: 4px;
    padding: 0;
    border: none;
    border-radius: 0;
    background: transparent;
    transition: all 0.2s ease;
    position: relative;
}

.theme-tool-message--panel {
    margin-bottom: 4px;
    padding: 0;
}

.theme-tool-message--sidebar {
    margin-bottom: 4px;
    padding: 0;
}

.theme-error-notice {
    margin: 0.5rem 0;
    padding: 0.75rem;
    background-color: var(--vscode-inputValidation-errorBackground);
    color: var(--vscode-inputValidation-errorForeground);
    border: 1px solid var(--vscode-inputValidation-errorBorder);
    border-left: 3px solid var(--vscode-errorForeground);
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-family: var(--vscode-editor-font-family);
}

/* Ensure theme preview cards use proper VSCode CSS variables */
.theme-tool-message .theme-preview-card {
    /* Override any hardcoded colors with VSCode theme variables */
    --background: var(--vscode-editor-background);
    --foreground: var(--vscode-editor-foreground);
    --muted: var(--vscode-input-background);
    --muted-foreground: var(--vscode-descriptionForeground);
    --border: var(--vscode-input-border);
    --card: var(--vscode-editorWidget-background);
    --card-foreground: var(--vscode-editorWidget-foreground);
    --primary: var(--vscode-textLink-foreground);
    --primary-foreground: var(--vscode-button-foreground);
    --secondary: var(--vscode-button-secondaryBackground);
    --secondary-foreground: var(--vscode-button-secondaryForeground);
    --accent: var(--vscode-list-hoverBackground);
    --accent-foreground: var(--vscode-list-hoverForeground);
    --destructive: var(--vscode-errorForeground);
    --destructive-foreground: var(--vscode-button-foreground);
    --ring: var(--vscode-focusBorder);
    --input: var(--vscode-input-background);
    --font-mono: var(--vscode-editor-font-family);
}

/* Responsive theme tool message styling */
@media (max-width: 768px) {
    .theme-tool-message--panel {
        margin-bottom: 16px;
    }
    
    .theme-tool-message--sidebar {
        margin-bottom: 10px;
    }
}

/* Tool result content */

/* Remove double margins when chat-message contains tool messages */
.chat-message--tool-container {
    margin-bottom: 0 !important;
    padding: 0 !important;
    background: transparent !important;
    border: none !important;
}

.chat-message--tool-container .chat-message__header {
    display: none !important;
}

.chat-message--tool-container .chat-message__content {
    padding: 0 !important;
}

/* Mixed content messages (text + tool calls) */
.chat-message--mixed-content {
    background: transparent;
    border: none;
    margin-bottom: 12px;
    padding: 0;
}

.chat-message--mixed-content .chat-message__content {
    margin-bottom: 6px;
}

.chat-message--mixed-content .chat-message__tools {
    margin-top: 4px;
}

.chat-message--mixed-content .chat-message__tools .tool-message {
    margin-bottom: 4px;
}

.chat-message--mixed-content .chat-message__tools .theme-tool-message {
    margin-bottom: 4px;
}

/* Multiple tool calls container */
.tool-calls-container {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.tool-calls-container .tool-message {
    margin-bottom: 0;
}

.tool-calls-container .theme-tool-message {
    margin-bottom: 0;
}

/* Theme copy button styling */
.theme-copy-btn:hover {
    background: var(--vscode-list-hoverBackground) !important;
    color: var(--vscode-foreground) !important;
}


 