#!/bin/bash

# Build VSIX Package Script
# This script builds the VS Code extension and packages it into a VSIX file

echo "🚀 Building Superdesign VS Code Extension..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo -e "${RED}❌ Error: package.json not found. Please run this script from the extension root directory.${NC}"
    exit 1
fi

# Check if vsce is installed
if ! command -v vsce &> /dev/null; then
    echo -e "${YELLOW}❌ vsce not found. Installing vsce globally...${NC}"
    npm install -g @vscode/vsce
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ Failed to install vsce. Please install it manually: npm install -g @vscode/vsce${NC}"
        exit 1
    fi
else
    VSCE_VERSION=$(vsce --version 2>/dev/null)
    echo -e "${GREEN}✅ Found vsce version: $VSCE_VERSION${NC}"
fi

# Clean previous builds
echo -e "${YELLOW}🧹 Cleaning previous builds...${NC}"
if [ -d "dist" ]; then
    rm -rf dist
fi
if ls *.vsix 1> /dev/null 2>&1; then
    rm -f *.vsix
fi

# Install dependencies
echo -e "${YELLOW}📦 Installing dependencies...${NC}"
npm install
if [ $? -ne 0 ]; then
    echo -e "${RED}❌ Failed to install dependencies${NC}"
    exit 1
fi

# Build the extension
echo -e "${YELLOW}🔨 Building extension...${NC}"
npm run package
if [ $? -ne 0 ]; then
    echo -e "${RED}❌ Build failed${NC}"
    exit 1
fi

# Package the extension
echo -e "${YELLOW}📦 Packaging VSIX...${NC}"
vsce package --no-dependencies
if [ $? -ne 0 ]; then
    echo -e "${RED}❌ VSIX packaging failed${NC}"
    exit 1
fi

# Find the generated VSIX file
VSIX_FILE=$(ls *.vsix 2>/dev/null | head -n 1)
if [ -n "$VSIX_FILE" ]; then
    echo -e "${GREEN}✅ Successfully created: $VSIX_FILE${NC}"
    echo -e "${CYAN}📍 Location: $(pwd)/$VSIX_FILE${NC}"
    
    # Show file size
    if command -v stat &> /dev/null; then
        if [[ "$OSTYPE" == "darwin"* ]]; then
            # macOS
            SIZE_BYTES=$(stat -f%z "$VSIX_FILE")
        else
            # Linux
            SIZE_BYTES=$(stat -c%s "$VSIX_FILE")
        fi
        SIZE_KB=$(echo "scale=2; $SIZE_BYTES / 1024" | bc 2>/dev/null || echo "$(($SIZE_BYTES / 1024))")
        SIZE_MB=$(echo "scale=2; $SIZE_BYTES / 1048576" | bc 2>/dev/null || echo "$(($SIZE_BYTES / 1048576))")
        echo -e "${CYAN}📏 Size: ${SIZE_KB} KB (${SIZE_MB} MB)${NC}"
    fi
    
    echo ""
    echo -e "${GREEN}🎉 Build completed successfully!${NC}"
    echo -e "${YELLOW}To install: code --install-extension $VSIX_FILE${NC}"
else
    echo -e "${RED}❌ VSIX file not found after packaging${NC}"
    exit 1
fi
