# Build VSIX Package Script
# This script builds the VS Code extension and packages it into a VSIX file

Write-Host "🚀 Building Superdesign VS Code Extension..." -ForegroundColor Green

# Check if we're in the right directory
if (!(Test-Path "package.json")) {
    Write-Host "❌ Error: package.json not found. Please run this script from the extension root directory." -ForegroundColor Red
    exit 1
}

# Check if vsce is installed
try {
    $vsceVersion = vsce --version 2>$null
    Write-Host "✅ Found vsce version: $vsceVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ vsce not found. Installing vsce globally..." -ForegroundColor Yellow
    npm install -g @vscode/vsce
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Failed to install vsce. Please install it manually: npm install -g @vscode/vsce" -ForegroundColor Red
        exit 1
    }
}

# Clean previous builds
Write-Host "🧹 Cleaning previous builds..." -ForegroundColor Yellow
if (Test-Path "dist") {
    Remove-Item -Recurse -Force "dist"
}
if (Test-Path "*.vsix") {
    Remove-Item -Force "*.vsix"
}

# Install dependencies
Write-Host "📦 Installing dependencies..." -ForegroundColor Yellow
npm install
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Failed to install dependencies" -ForegroundColor Red
    exit 1
}

# Build the extension
Write-Host "🔨 Building extension..." -ForegroundColor Yellow
npm run package
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Build failed" -ForegroundColor Red
    exit 1
}

# Package the extension
Write-Host "📦 Packaging VSIX..." -ForegroundColor Yellow
vsce package --no-dependencies
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ VSIX packaging failed" -ForegroundColor Red
    exit 1
}

# Find the generated VSIX file
$vsixFile = Get-ChildItem -Filter "*.vsix" | Select-Object -First 1
if ($vsixFile) {
    Write-Host "✅ Successfully created: $($vsixFile.Name)" -ForegroundColor Green
    Write-Host "📍 Location: $($vsixFile.FullName)" -ForegroundColor Cyan
    
    # Show file size
    $sizeKB = [math]::Round($vsixFile.Length / 1KB, 2)
    $sizeMB = [math]::Round($vsixFile.Length / 1MB, 2)
    Write-Host "📏 Size: $sizeKB KB ($sizeMB MB)" -ForegroundColor Cyan
    
    Write-Host ""
    Write-Host "🎉 Build completed successfully!" -ForegroundColor Green
    Write-Host "To install: code --install-extension $($vsixFile.Name)" -ForegroundColor Yellow
} else {
    Write-Host "❌ VSIX file not found after packaging" -ForegroundColor Red
    exit 1
}
