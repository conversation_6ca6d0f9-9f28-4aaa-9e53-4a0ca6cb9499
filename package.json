{"name": "superdesign", "displayName": "superdesign", "description": "The 1st Design Agent lives inside your IDE", "version": "0.0.11", "publisher": "iganbold", "icon": "icon.png", "repository": {"type": "git", "url": "https://github.com/superdesigndev/superdesign"}, "engines": {"vscode": "^1.90.0"}, "categories": ["AI", "Other"], "keywords": ["design", "ui", "mockup", "wireframe", "component", "ai", "agent", "figma", "prototype", "layout", "frontend", "react", "html", "css", "claude", "anthropic", "generator", "automation"], "activationEvents": ["onCommand:superdesign.helloWorld", "onView:superdesign.chatView"], "main": "./dist/extension.js", "contributes": {"commands": [{"command": "superdesign.helloWorld", "title": "Hello World", "category": "Superdesign"}, {"command": "superdesign.configureApiKey", "title": "Configure Anthropic API Key", "category": "Superdesign"}, {"command": "superdesign.configureOpenAIApiKey", "title": "Configure OpenAI API Key", "category": "Superdesign"}, {"command": "superdesign.configureOpenRouterApiKey", "title": "Configure OpenRouter API Key", "category": "Superdesign"}, {"command": "superdesign.configureCustomOpenAI", "title": "Configure Custom OpenAI Endpoint", "category": "Superdesign"}, {"command": "superdesign.showChatSidebar", "title": "Show Chat Sidebar", "category": "Superdesign"}, {"command": "superdesign.openCanvas", "title": "Open Canvas View", "category": "Superdesign", "icon": "$(window)"}, {"command": "superdesign.clearChat", "title": "Clear Chat", "category": "Superdesign", "icon": "$(clear-all)"}, {"command": "superdesign.resetWelcome", "title": "Reset Welcome Screen", "category": "Superdesign"}, {"command": "superdesign.initializeProject", "title": "Initialize Superdesign", "category": "Superdesign"}, {"command": "superdesign.openSettings", "title": "Open Settings", "category": "Superdesign", "icon": "$(settings-gear)"}], "menus": {"view/title": [{"command": "superdesign.openCanvas", "when": "view == superdesign.chatView", "group": "navigation"}, {"command": "superdesign.openSettings", "when": "view == superdesign.chatView", "group": "navigation"}, {"command": "superdesign.clearChat", "when": "view == superdesign.chatView", "group": "navigation"}]}, "views": {"superdesign-sidebar": [{"type": "webview", "id": "superdesign.chatView", "name": "SUPER DESIGN", "when": "true"}]}, "viewsContainers": {"activitybar": [{"id": "superdesign-sidebar", "title": "Superdesign", "icon": "icon.svg"}]}, "configuration": {"title": "Superdesign", "properties": {"superdesign.anthropicApiKey": {"type": "string", "description": "Anthropic API key for Claude Code integration", "scope": "application"}, "superdesign.openaiApiKey": {"type": "string", "description": "OpenAI API key for custom agent", "scope": "application"}, "superdesign.openrouterApiKey": {"type": "string", "description": "OpenRouter API key for custom agent", "scope": "application"}, "superdesign.aiModelProvider": {"type": "string", "enum": ["openai", "anthropic", "openrouter", "custom-openai"], "default": "anthropic", "description": "AI model provider for custom agent (OpenAI, Anthropic, OpenRouter, or Custom OpenAI-compatible)", "scope": "application"}, "superdesign.customOpenaiBaseUrl": {"type": "string", "description": "Custom base URL for OpenAI-compatible API (e.g., http://localhost:8989/v1)", "scope": "application"}, "superdesign.customOpenaiApiKey": {"type": "string", "description": "API key for custom OpenAI-compatible endpoint", "scope": "application"}, "superdesign.aiModel": {"type": "string", "description": "Specific AI model to use (e.g., gpt-4o, claude-3-5-sonnet-20241022)", "scope": "application"}}}}, "scripts": {"vscode:prepublish": "npm run package", "compile": "npm run check-types && npm run lint && node esbuild.js", "watch": "npm-run-all -p watch:*", "watch:esbuild": "node esbuild.js --watch", "watch:tsc": "tsc --noEmit --watch --project tsconfig.json", "package": "npm run check-types && npm run lint && node esbuild.js --production", "compile-tests": "tsc -p . --outDir out", "watch-tests": "tsc -p . -w --outDir out", "pretest": "npm run compile-tests && npm run compile && npm run lint", "check-types": "tsc --noEmit", "lint": "eslint src", "test": "vscode-test", "test:llm": "tsc --project tsconfig.test.json && node dist-test/test/llm-service.test.js", "test:core": "tsc --project tsconfig.test.json && node dist-test/test/core-components.test.js", "test:read": "tsc --project tsconfig.test.json && node dist-test/test/read-tool.test.js", "test:write-edit": "tsc --project tsconfig.test.json && node dist-test/test/write-edit-tools.test.js", "test:ls-grep-glob": "tsc --project tsconfig.test.json && node dist-test/test/ls-grep-glob-tools.test.js", "test:agent": "tsc --project tsconfig.test.json && node dist-test/test/llm-service.test.js && node dist-test/test/core-components.test.js", "test:tools": "tsc --project tsconfig.test.json && node dist-test/test/read-tool.test.js && node dist-test/test/write-edit-tools.test.js && node dist-test/test/ls-grep-glob-tools.test.js"}, "devDependencies": {"@types/mime-types": "^3.0.1", "@types/mocha": "^10.0.10", "@types/node": "20.x", "@types/vscode": "^1.90.0", "@typescript-eslint/eslint-plugin": "^8.31.1", "@typescript-eslint/parser": "^8.31.1", "@vscode/test-cli": "^0.0.10", "@vscode/test-electron": "^2.5.2", "esbuild": "^0.25.3", "eslint": "^9.25.1", "npm-run-all": "^4.1.5", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "dependencies": {"@ai-sdk/anthropic": "^1.2.12", "@ai-sdk/google": "^1.2.19", "@ai-sdk/openai": "^1.3.22", "@anthropic-ai/claude-code": "^1.0.31", "@openrouter/ai-sdk-provider": "^0.7.2", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "ai": "^4.3.16", "execa": "^9.6.0", "glob": "^11.0.3", "highlight.js": "^11.11.1", "lucide-react": "^0.522.0", "micromatch": "^4.0.8", "mime-types": "^3.0.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-markdown": "^10.1.0", "react-zoom-pan-pinch": "^3.7.0", "rehype-highlight": "^7.0.2", "remark-gfm": "^4.0.1", "zod": "^3.25.67"}}