const http = require('http');

async function testExactCall() {
    console.log('Testing exact call that extension makes...\n');
    
    const payload = JSON.stringify({
        model: 'claude-sonnet-4-20250514',
        messages: [
            {
                role: 'user',
                content: 'Hello'
            }
        ],
        max_tokens: 50
    });

    const options = {
        hostname: 'localhost',
        port: 8989,
        path: '/v1/chat/completions',
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer ki2api-key-2024',
            'Content-Length': Buffer.byteLength(payload)
        }
    };

    console.log('Request details:');
    console.log('URL:', `http://${options.hostname}:${options.port}${options.path}`);
    console.log('Headers:', options.headers);
    console.log('Payload:', payload);
    console.log('\n');

    return new Promise((resolve) => {
        const req = http.request(options, (res) => {
            console.log(`Status: ${res.statusCode} ${res.statusMessage}`);
            console.log('Response Headers:', res.headers);
            
            let data = '';
            res.on('data', (chunk) => {
                data += chunk;
            });

            res.on('end', () => {
                console.log('Response Body:', data);
                
                if (res.statusCode === 200) {
                    console.log('\n✅ SUCCESS! This should work in the extension');
                } else {
                    console.log('\n❌ ERROR! This is why extension shows "Unauthorized"');
                    
                    // Check if it's actually an auth error or something else
                    if (res.statusCode === 401) {
                        console.log('🔍 This is a real authentication error');
                    } else if (res.statusCode === 400) {
                        console.log('🔍 This is a bad request error (likely model name issue)');
                    } else if (res.statusCode === 404) {
                        console.log('🔍 This is a not found error (likely wrong endpoint path)');
                    } else {
                        console.log(`🔍 This is a ${res.statusCode} error`);
                    }
                }
                
                resolve();
            });
        });

        req.on('error', (error) => {
            console.log('Connection Error:', error.message);
            resolve();
        });

        req.setTimeout(10000);
        req.write(payload);
        req.end();
    });
}

testExactCall().catch(console.error);
