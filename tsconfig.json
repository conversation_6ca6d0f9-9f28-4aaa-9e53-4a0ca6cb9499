{
	"compilerOptions": {
	  "module": "ESNext",
	  "target": "ES2020",
	  "outDir": "dist",
	  "lib": ["ES2020", "DOM", "DOM.Iterable"],
	  "sourceMap": true,
	  "rootDir": "src",
	  "strict": true,
	  "jsx": "react-jsx",
	  "esModuleInterop": true,
	  "skipLibCheck": true,
	  "forceConsistentCasingInFileNames": true,
	  "allowSyntheticDefaultImports": true,
	  "moduleResolution": "node",
	  "resolveJsonModule": true,
	  "declaration": false,
	  "noEmit": false
	},
	"include": [
	  "src/**/*"
	],
	"exclude": [
	  "node_modules",
	  "dist",
	  ".vscode-test"
	]
	/* Additional Checks */
	// "noImplicitReturns": true, /* Report error when not all code paths in function return a value. */
	// "noFallthroughCasesInSwitch": true, /* Report errors for fallthrough cases in switch statement. */
	// "noUnusedParameters": true,  /* Report errors on unused parameters. */
}
