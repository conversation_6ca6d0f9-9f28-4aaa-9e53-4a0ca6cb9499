const http = require('http');
const fs = require('fs');
const path = require('path');

class ClaudeCodeRequestCapture {
    constructor(port = 8080) {
        this.port = port;
        this.capturedRequests = [];
        this.logFile = path.join(__dirname, 'claude-code-requests.json');
        
        // Create server
        this.server = http.createServer((req, res) => {
            this.handleRequest(req, res);
        });
    }

    async handleRequest(req, res) {
        const timestamp = new Date().toISOString();
        const requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        
        console.log(`\n${'='.repeat(80)}`);
        console.log(`🔍 NEW REQUEST CAPTURED - ${timestamp}`);
        console.log(`📝 Request ID: ${requestId}`);
        console.log(`${'='.repeat(80)}`);
        
        // Capture request details
        const requestData = {
            id: requestId,
            timestamp,
            method: req.method,
            url: req.url,
            httpVersion: req.httpVersion,
            headers: { ...req.headers },
            body: null,
            rawBody: null
        };

        // Log basic request info
        console.log(`🌐 Method: ${req.method}`);
        console.log(`🔗 URL: ${req.url}`);
        console.log(`📋 HTTP Version: ${req.httpVersion}`);
        console.log(`🖥️  Remote Address: ${req.connection.remoteAddress}`);
        console.log(`🔌 Remote Port: ${req.connection.remotePort}`);
        
        // Log headers in detail
        console.log(`\n📊 HEADERS (${Object.keys(req.headers).length} total):`);
        console.log('─'.repeat(50));
        Object.entries(req.headers).forEach(([key, value]) => {
            const isAuth = key.toLowerCase().includes('auth') || 
                          key.toLowerCase().includes('key') || 
                          key.toLowerCase().includes('token');
            
            if (isAuth) {
                console.log(`🔐 ${key}: ${this.maskSensitive(value)}`);
            } else {
                console.log(`📋 ${key}: ${value}`);
            }
        });

        // Capture request body
        let body = '';
        let rawChunks = [];
        
        req.on('data', (chunk) => {
            rawChunks.push(chunk);
            body += chunk.toString();
        });

        req.on('end', () => {
            const rawBody = Buffer.concat(rawChunks);
            requestData.rawBody = rawBody.toString('base64');
            
            // Try to parse as JSON
            try {
                const jsonBody = JSON.parse(body);
                requestData.body = jsonBody;
                
                console.log(`\n💾 BODY (JSON Parsed):`);
                console.log('─'.repeat(50));
                console.log(JSON.stringify(jsonBody, null, 2));
                
                // Log specific Claude Code fields
                if (jsonBody.model) {
                    console.log(`\n🤖 AI Model: ${jsonBody.model}`);
                }
                if (jsonBody.messages) {
                    console.log(`📨 Messages Count: ${jsonBody.messages.length}`);
                }
                if (jsonBody.tools) {
                    console.log(`🔧 Tools Count: ${jsonBody.tools.length}`);
                }
                if (jsonBody.max_tokens) {
                    console.log(`📏 Max Tokens: ${jsonBody.max_tokens}`);
                }
                
            } catch (e) {
                requestData.body = body;
                console.log(`\n💾 BODY (Raw Text, ${body.length} chars):`);
                console.log('─'.repeat(50));
                console.log(body.substring(0, 1000) + (body.length > 1000 ? '...[truncated]' : ''));
            }

            // Analyze authentication
            this.analyzeAuthentication(requestData);
            
            // Save to memory and file
            this.capturedRequests.push(requestData);
            this.saveToFile(requestData);
            
            // Send response
            this.sendResponse(res, requestData);
            
            console.log(`\n✅ Request captured and saved!`);
            console.log(`${'='.repeat(80)}\n`);
        });

        req.on('error', (err) => {
            console.error(`❌ Request error: ${err.message}`);
            res.writeHead(500, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ error: 'Request processing failed', details: err.message }));
        });
    }

    analyzeAuthentication(requestData) {
        console.log(`\n🔐 AUTHENTICATION ANALYSIS:`);
        console.log('─'.repeat(50));
        
        const headers = requestData.headers;
        const authMethods = [];
        
        // Check Authorization header
        if (headers.authorization) {
            const authType = headers.authorization.split(' ')[0];
            authMethods.push(`Authorization: ${authType} [${this.maskSensitive(headers.authorization)}]`);
        }
        
        // Check API key headers
        const apiKeyHeaders = ['x-api-key', 'anthropic-api-key', 'openai-api-key'];
        apiKeyHeaders.forEach(header => {
            if (headers[header]) {
                authMethods.push(`${header}: ${this.maskSensitive(headers[header])}`);
            }
        });
        
        // Check custom headers
        Object.entries(headers).forEach(([key, value]) => {
            if (key.toLowerCase().includes('auth') || key.toLowerCase().includes('key') || key.toLowerCase().includes('token')) {
                if (!apiKeyHeaders.includes(key.toLowerCase()) && key.toLowerCase() !== 'authorization') {
                    authMethods.push(`${key}: ${this.maskSensitive(value)}`);
                }
            }
        });
        
        if (authMethods.length > 0) {
            authMethods.forEach(method => console.log(`🔑 ${method}`));
        } else {
            console.log('⚠️  No authentication methods detected');
        }
        
        // Check User-Agent for Claude Code signature
        if (headers['user-agent']) {
            console.log(`🔍 User-Agent: ${headers['user-agent']}`);
            if (headers['user-agent'].includes('claude-code')) {
                console.log('✅ Claude Code User-Agent detected!');
            }
        }
    }

    maskSensitive(value) {
        if (!value || value.length < 8) return '[MASKED]';
        return value.substring(0, 12) + '*'.repeat(Math.max(0, value.length - 16)) + value.substring(Math.max(12, value.length - 4));
    }

    saveToFile(requestData) {
        try {
            // Save individual request
            const filename = `claude-request-${requestData.id}.json`;
            fs.writeFileSync(filename, JSON.stringify(requestData, null, 2));
            
            // Update master log
            let allRequests = [];
            if (fs.existsSync(this.logFile)) {
                allRequests = JSON.parse(fs.readFileSync(this.logFile, 'utf8'));
            }
            allRequests.push({
                id: requestData.id,
                timestamp: requestData.timestamp,
                method: requestData.method,
                url: requestData.url,
                headers: Object.keys(requestData.headers),
                hasBody: !!requestData.body,
                filename: filename
            });
            fs.writeFileSync(this.logFile, JSON.stringify(allRequests, null, 2));
            
            console.log(`💾 Saved to: ${filename}`);
            
        } catch (error) {
            console.error(`❌ Failed to save request: ${error.message}`);
        }
    }

    sendResponse(res, requestData) {
        // Send a mock Claude response to keep the client happy
        const mockResponse = {
            type: "message",
            id: `msg_${Date.now()}`,
            role: "assistant",
            content: [
                {
                    type: "text",
                    text: `🔍 Request captured successfully!\n\nRequest ID: ${requestData.id}\nTimestamp: ${requestData.timestamp}\nMethod: ${requestData.method}\nURL: ${requestData.url}\n\nThis is a mock response from the Claude Code request capture server. Your actual request has been logged and saved for analysis.`
                }
            ],
            model: requestData.body?.model || "claude-3-5-sonnet-20241022",
            stop_reason: "end_turn",
            stop_sequence: null,
            usage: {
                input_tokens: 100,
                output_tokens: 50
            }
        };

        res.writeHead(200, {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
            'Access-Control-Allow-Headers': '*'
        });
        
        res.end(JSON.stringify(mockResponse, null, 2));
    }

    start() {
        this.server.listen(this.port, () => {
            console.log(`🚀 Claude Code Request Capture Server Started`);
            console.log(`${'='.repeat(60)}`);
            console.log(`🌐 Server running on: http://localhost:${this.port}`);
            console.log(`📝 Logs will be saved to: ${this.logFile}`);
            console.log(`${'='.repeat(60)}`);
            console.log(`\n📋 Setup Instructions:`);
            console.log(`1. Set environment variable:`);
            console.log(`   export ANTHROPIC_BASE_URL=http://localhost:${this.port}`);
            console.log(`2. Run your Claude Code command`);
            console.log(`3. Check the captured requests in this terminal and saved files`);
            console.log(`\n⏳ Waiting for requests...\n`);
        });

        // Handle server errors
        this.server.on('error', (err) => {
            console.error(`❌ Server error: ${err.message}`);
            if (err.code === 'EADDRINUSE') {
                console.log(`🔄 Port ${this.port} is in use. Trying port ${this.port + 1}...`);
                this.port++;
                this.start();
            }
        });

        // Graceful shutdown
        process.on('SIGINT', () => {
            console.log(`\n\n🛑 Shutting down server...`);
            console.log(`📊 Total requests captured: ${this.capturedRequests.length}`);
            this.server.close(() => {
                console.log(`✅ Server stopped gracefully`);
                process.exit(0);
            });
        });
    }

    generateSimulationCode() {
        if (this.capturedRequests.length === 0) {
            console.log('⚠️  No requests captured yet');
            return;
        }

        const latestRequest = this.capturedRequests[this.capturedRequests.length - 1];
        const code = `
// Generated Claude Code simulation based on captured request
const https = require('https');

const simulateClaudeCodeRequest = async () => {
    const options = {
        hostname: 'api.anthropic.com', // or your target host
        port: 443,
        path: '${latestRequest.url}',
        method: '${latestRequest.method}',
        headers: ${JSON.stringify(latestRequest.headers, null, 8)}
    };

    const postData = ${JSON.stringify(latestRequest.body, null, 4)};

    return new Promise((resolve, reject) => {
        const req = https.request(options, (res) => {
            let data = '';
            res.on('data', chunk => data += chunk);
            res.on('end', () => resolve(JSON.parse(data)));
        });
        
        req.on('error', reject);
        req.write(JSON.stringify(postData));
        req.end();
    });
};

module.exports = { simulateClaudeCodeRequest };
        `;

        const filename = `claude-code-simulation-${Date.now()}.js`;
        fs.writeFileSync(filename, code);
        console.log(`🎯 Generated simulation code: ${filename}`);
    }
}

// Start the server
const capture = new ClaudeCodeRequestCapture(8080);
capture.start();

// Generate simulation code command
process.on('SIGUSR1', () => {
    capture.generateSimulationCode();
});