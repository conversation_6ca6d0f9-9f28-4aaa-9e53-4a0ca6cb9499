/* Welcome Section Styles - Following SuperDesign Design System */
.welcome-section {
    padding: 20px 16px;
    background: var(--vscode-sideBar-background);
    color: var(--vscode-sideBar-foreground);
    height: 90%;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 24px;
    text-align: center;
}

.welcome-header {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.welcome-logo {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
}

.logo-icon {
    font-size: 24px;
    width: 48px;
    height: 48px;
    background: var(--vscode-button-background);
    color: var(--vscode-button-foreground);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 6px;
}

.welcome-header h1 {
    margin: 0 0 6px 0;
    font-size: 20px;
    font-weight: 600;
    color: var(--vscode-foreground);
    font-family: var(--vscode-font-family);
}

.welcome-subtitle {
    font-size: 14px;
    color: var(--vscode-descriptionForeground);
    margin: 0;
    font-weight: 400;
    line-height: 1.4;
    max-width: 300px;
}

/* Form Styles */
.welcome-form {
    display: flex;
    flex-direction: column;
    gap: 16px;
    width: 100%;
    max-width: 260px;
}

.email-input-group {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.email-input {
    padding: 8px 12px;
    border: 1px solid var(--vscode-input-border);
    border-radius: 4px;
    background: var(--vscode-input-background);
    color: var(--vscode-input-foreground);
    font-size: 12px;
    font-family: var(--vscode-font-family);
    transition: border-color 0.2s ease;
    width: 100%;
    box-sizing: border-box;
}

.email-input:focus {
    outline: none;
    border-color: var(--vscode-focusBorder);
    background: var(--vscode-input-background);
}

.email-input::placeholder {
    color: var(--vscode-input-placeholderForeground);
}

.email-input:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.email-input.error {
    border-color: var(--vscode-inputValidation-errorBorder);
    background: var(--vscode-inputValidation-errorBackground);
}

.error-message {
    font-size: 11px;
    color: var(--vscode-inputValidation-errorForeground);
    font-family: var(--vscode-font-family);
    text-align: left;
    margin-top: 2px;
}

.welcome-actions {
    display: flex;
    justify-content: center;
}

/* Button styles following VS Code design system */
.btn-primary, .btn-secondary {
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    font-family: var(--vscode-font-family);
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
    min-width: 100px;
    text-align: center;
}

.btn-primary {
    background: var(--vscode-button-background);
    color: var(--vscode-button-foreground);
}

.btn-primary:hover:not(:disabled) {
    background: var(--vscode-button-hoverBackground);
}

.btn-primary:active:not(:disabled) {
    background: var(--vscode-button-activeBackground);
}

.btn-primary:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    background: var(--vscode-button-background);
}

.btn-secondary {
    background: transparent;
    color: var(--vscode-foreground);
    border: 1px solid var(--vscode-input-border);
}

.btn-secondary:hover {
    background: var(--vscode-list-hoverBackground);
    border-color: var(--vscode-focusBorder);
}

.btn-secondary:active {
    background: var(--vscode-list-activeSelectionBackground);
}

/* Responsive adjustments */
@media (max-width: 480px) {
    .welcome-section {
        padding: 16px;
        gap: 24px;
    }
    
    .welcome-header h1 {
        font-size: 20px;
    }
    
    .welcome-subtitle {
        font-size: 13px;
    }
    
    .welcome-form {
        max-width: 100%;
    }
    
    .btn-primary {
        width: 100%;
    }
}

/* Scrollbar styling to match VS Code */
.welcome-section::-webkit-scrollbar {
    width: 6px;
}

.welcome-section::-webkit-scrollbar-track {
    background: transparent;
}

.welcome-section::-webkit-scrollbar-thumb {
    background: var(--vscode-scrollbarSlider-background);
    border-radius: 3px;
}

.welcome-section::-webkit-scrollbar-thumb:hover {
    background: var(--vscode-scrollbarSlider-hoverBackground);
}

/* Keep the feature/step styles for potential future use */
.welcome-features {
    /* Features section spacing */
}

.feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 16px;
}

.feature-card {
    background: var(--vscode-input-background);
    border: 1px solid var(--vscode-input-border);
    border-radius: 8px;
    padding: 16px;
    transition: all 0.2s ease;
    cursor: default;
    text-align: center;
}

.feature-card:hover {
    background: var(--vscode-list-hoverBackground);
    border-color: var(--vscode-focusBorder);
}

.feature-icon {
    font-size: 24px;
    margin-bottom: 8px;
    display: block;
}

.feature-card h3 {
    margin: 0 0 6px 0;
    font-size: 14px;
    font-weight: 600;
    color: var(--vscode-foreground);
    font-family: var(--vscode-font-family);
}

.feature-card p {
    margin: 0;
    font-size: 12px;
    color: var(--vscode-descriptionForeground);
    line-height: 1.3;
}

.welcome-getting-started h3 {
    margin: 0 0 16px 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--vscode-foreground);
    font-family: var(--vscode-font-family);
    text-align: center;
}

.getting-started-steps {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.step {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: var(--vscode-input-background);
    border: 1px solid var(--vscode-input-border);
    border-radius: 8px;
}

.step-number {
    width: 24px;
    height: 24px;
    background: var(--vscode-button-background);
    color: var(--vscode-button-foreground);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 12px;
    flex-shrink: 0;
}

.step p {
    margin: 0;
    font-size: 12px;
    color: var(--vscode-foreground);
    line-height: 1.3;
} 