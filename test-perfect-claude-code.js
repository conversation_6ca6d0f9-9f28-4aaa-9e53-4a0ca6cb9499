const https = require('https');

async function testPerfectClaudeCodeSimulation() {
    console.log('🎯 Testing PERFECT Claude Code simulation with exact captured format...\n');
    
    // Try different token formats
    const userApiKey = 'sk-5UeqYtwdz9d5iaY5nmUXXxHwhz6g8H1saFDMiURfrVE1TRRA';
    const capturedToken = '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';

    // Test with user's API key first (might work with anyrouter)
    const oauthKey = userApiKey;
    
    // Use exact user ID from captured request
    const userId = 'user_6ae05a6d1b2ecab361843986e8aa77449157b3e11d726d7aa75aff60c915f701_account_adb4c9ef-74c3-45fe-9382-450ced5af0e7_session_4a1ccdc1-79ff-4676-b464-974d0bf49b3d';
    
    // EXACT request body format from captured request
    const requestData = {
        model: 'claude-3-5-haiku-********',
        max_tokens: 512,
        messages: [
            {
                role: 'user',
                content: 'Hello, respond with "Claude Code simulation working!" to test the connection.'
            }
        ],
        system: [
            {
                type: 'text',
                text: 'You are a helpful AI assistant.',
                cache_control: {
                    type: 'ephemeral'
                }
            }
        ],
        temperature: 0,
        metadata: {
            user_id: userId
        },
        stream: true
    };

    // EXACT headers from captured request
    const options = {
        hostname: 'pmpjfbhq.cn-nb1.rainapp.top',
        port: 443,
        path: '/v1/messages?beta=true',
        method: 'POST',
        headers: {
            'host': 'pmpjfbhq.cn-nb1.rainapp.top',
            'connection': 'keep-alive',
            'accept': 'application/json',
            'anthropic-beta': 'claude-code-20250219,oauth-2025-04-20,interleaved-thinking-2025-05-14,fine-grained-tool-streaming-2025-05-14',
            'anthropic-dangerous-direct-browser-access': 'true',
            'anthropic-version': '2023-06-01',
            'authorization': `Bearer ${oauthKey}`,
            'content-type': 'application/json',
            'user-agent': 'claude-cli/1.0.60 (external, cli)',
            'x-app': 'cli',
            'x-stainless-arch': 'x64',
            'x-stainless-helper-method': 'stream',  // MISSING in previous attempts!
            'x-stainless-lang': 'js',
            'x-stainless-os': 'Linux',
            'x-stainless-package-version': '0.55.1',
            'x-stainless-retry-count': '0',
            'x-stainless-runtime': 'node',
            'x-stainless-runtime-version': 'v18.19.1',
            'x-stainless-timeout': '60',
            'accept-language': '*',
            'sec-fetch-mode': 'cors',
            'accept-encoding': 'gzip, deflate',
            'content-length': Buffer.byteLength(JSON.stringify(requestData)).toString()
        }
    };

    return new Promise((resolve, reject) => {
        console.log('🚀 Sending PERFECT Claude Code request...');
        console.log('🔗 URL:', `https://${options.hostname}${options.path}`);
        console.log('🔑 Auth:', options.headers.authorization.substring(0, 30) + '...');
        console.log('🤖 Model:', requestData.model);
        console.log('🌡️  Temperature:', requestData.temperature);
        console.log('📡 Stream:', requestData.stream);
        console.log('👤 User ID:', requestData.metadata.user_id.substring(0, 50) + '...');
        console.log('📦 System messages:', requestData.system.length);
        console.log('🔧 Helper method:', options.headers['x-stainless-helper-method']);
        console.log('');
        console.log('Full Request Options:', JSON.stringify(options, null, 2));
        console.log('');
        
        const req = https.request(options, (res) => {
            console.log(`📊 Status: ${res.statusCode}`);
            console.log('📋 Response Headers:');
            Object.entries(res.headers).forEach(([key, value]) => {
                console.log(`   ${key}: ${value}`);
            });
            console.log('');
            
            let data = '';
            res.on('data', (chunk) => {
                data += chunk;
                // Show streaming data in real-time for stream responses
                if (res.headers['content-type']?.includes('text/plain') || 
                    res.headers['content-type']?.includes('text/event-stream')) {
                    process.stdout.write(chunk);
                }
            });
            
            res.on('end', () => {
                console.log('📨 Response Body:');
                console.log(data);
                console.log('');
                
                if (res.statusCode === 200) {
                    try {
                        const parsed = JSON.parse(data);
                        console.log('✅ SUCCESS: Got valid JSON response!');
                        console.log('🎯 Response type:', parsed.type || 'unknown');
                        if (parsed.content && parsed.content[0]) {
                            console.log('💬 Content:', parsed.content[0].text?.substring(0, 100) + '...');
                        }
                        resolve(parsed);
                    } catch (e) {
                        // Might be streaming format
                        if (data.includes('data:') || data.includes('event:')) {
                            console.log('✅ SUCCESS: Got streaming response!');
                            resolve({ streaming: true, data: data });
                        } else {
                            console.log('⚠️  Got response but not valid JSON');
                            resolve(data);
                        }
                    }
                } else {
                    console.log(`❌ FAILED: HTTP ${res.statusCode}`);
                    
                    // Analyze the error
                    if (data.includes('invalid claude code request')) {
                        console.log('🔍 Analysis: Format almost correct, minor issue remaining');
                        console.log('🧐 Check: OAuth token format, missing headers, or field order');
                    } else if (data.includes('denied by http_custom')) {
                        console.log('🚫 Analysis: Anti-bot protection still active');
                    } else if (res.statusCode === 401) {
                        console.log('🔐 Analysis: Authentication failed');
                    } else if (res.statusCode === 404) {
                        console.log('🔍 Analysis: Endpoint not found');
                    }
                    
                    resolve({ error: `HTTP ${res.statusCode}`, body: data });
                }
            });
        });

        req.on('error', (error) => {
            console.error('❌ Request error:', error.message);
            reject(error);
        });

        console.log('📤 Sending request body...');
        console.log(JSON.stringify(requestData, null, 2));
        console.log('');
        
        req.write(JSON.stringify(requestData));
        req.end();
    });
}

testPerfectClaudeCodeSimulation().catch(console.error);